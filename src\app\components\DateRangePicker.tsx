import React, { useState, useEffect } from "react";

interface DateRangePickerProps {
    availableDates: string[];
    selectedRange: { start: string; end: string } | null;
    onRangeChange: (range: { start: string; end: string } | null) => void;
    customButtonText?: string;
    customButtonClassName?: string;
}

export const DateRangePicker: React.FC<DateRangePickerProps> = ({
    availableDates,
    selectedRange,
    onRangeChange,
    customButtonText,
    customButtonClassName,
}) => {
    const [isOpen, setIsOpen] = useState(false);
    const [currentMonth, setCurrentMonth] = useState<Date>(() => {
        // Initialize to current month
        return new Date();
    });
    const [hoveredDate, setHoveredDate] = useState<string | null>(null);
    const [tempStartDate, setTempStartDate] = useState<string | null>(selectedRange?.start || null);
    const [tempEndDate, setTempEndDate] = useState<string | null>(selectedRange?.end || null);

    // Sync internal state with external selectedRange changes
    useEffect(() => {
        setTempStartDate(selectedRange?.start || null);
        setTempEndDate(selectedRange?.end || null);
    }, [selectedRange]);

    const handleDateClick = (dateStr: string) => {
        if (!tempStartDate || (tempStartDate && tempEndDate)) {
            // Start a new selection
            setTempStartDate(dateStr);
            setTempEndDate(null);
        } else {
            // Complete the selection
            const start = new Date(tempStartDate);
            const clicked = new Date(dateStr);

            // Normalize dates to avoid time zone issues
            start.setHours(0, 0, 0, 0);
            clicked.setHours(0, 0, 0, 0);

            if (clicked < start) {
                setTempEndDate(tempStartDate);
                setTempStartDate(dateStr);
            } else {
                setTempEndDate(dateStr);
            }
        }
    };

    const handleMouseEnter = (dateStr: string) => {
        setHoveredDate(dateStr);
    };

    const handleMouseLeave = () => {
        setHoveredDate(null);
    };

    const handleApply = () => {
        if (tempStartDate && tempEndDate) {
            onRangeChange({ start: tempStartDate, end: tempEndDate });
        } else if (tempStartDate) {
            onRangeChange({ start: tempStartDate, end: tempStartDate });
        }
        setIsOpen(false);
    };

    const handleClear = () => {
        setTempStartDate(null);
        setTempEndDate(null);
        onRangeChange(null);
        setIsOpen(false);
    };

    const getDisplayText = () => {
        if (selectedRange) {
            return `${selectedRange.start} - ${selectedRange.end}`;
        }
        return "Select date range";
    };

    // Check if a date should be shown as selected in the calendar
    const isDateSelected = (dateStr: string): boolean => {
        if (!tempStartDate) return false;
        if (tempStartDate === dateStr) return true;
        if (tempEndDate && tempStartDate <= dateStr && dateStr <= tempEndDate) return true;
        if (!tempEndDate && hoveredDate) {
            const start = tempStartDate < hoveredDate ? tempStartDate : hoveredDate;
            const end = tempStartDate < hoveredDate ? hoveredDate : tempStartDate;
            return dateStr >= start && dateStr <= end;
        }
        return false;
    };

    // Check if a date is in the available dates array
    const isDateAvailable = (dateStr: string): boolean => {
        return availableDates.includes(dateStr);
    };

    // Generate calendar for current month
    const renderCalendar = () => {
        const firstDayOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
        const lastDayOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0);

        // Start from Monday (1) instead of Sunday (0)
        const firstDayOfWeek = firstDayOfMonth.getDay();
        const daysFromMonday = firstDayOfWeek === 0 ? 6 : firstDayOfWeek - 1;

        const calendarDays: string[] = [];

        // Add days from previous month
        const prevMonth = new Date(currentMonth);
        prevMonth.setMonth(prevMonth.getMonth() - 1);
        const lastDayOfPrevMonth = new Date(prevMonth.getFullYear(), prevMonth.getMonth() + 1, 0).getDate();

        for (let i = daysFromMonday - 1; i >= 0; i--) {
            const day = lastDayOfPrevMonth - i;
            const dateStr = formatDateLocal(new Date(prevMonth.getFullYear(), prevMonth.getMonth(), day));
            calendarDays.push(dateStr);
        }

        // Add days from current month
        for (let day = 1; day <= lastDayOfMonth.getDate(); day++) {
            const dateStr = formatDateLocal(new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day));
            calendarDays.push(dateStr);
        }

        // Add days from next month to complete the grid (42 days total for 6 rows)
        const nextMonth = new Date(currentMonth);
        nextMonth.setMonth(nextMonth.getMonth() + 1);
        const daysToAdd = 42 - calendarDays.length;

        for (let i = 1; i <= daysToAdd; i++) {
            // Use the constructor with year, month, day to avoid timezone issues
            const date = new Date(nextMonth.getFullYear(), nextMonth.getMonth(), i);
            const dateStr = formatDateLocal(date);
            calendarDays.push(dateStr);
        }

        // Split into weeks
        const weeks: string[][] = [];
        for (let i = 0; i < calendarDays.length; i += 7) {
            weeks.push(calendarDays.slice(i, i + 7));
        }

        return weeks;
    };

    // Use local date formatting to avoid timezone issues
    const formatDateLocal = (date: Date): string => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    };

    const formatMonthYear = (date: Date): string => {
        return date.toLocaleDateString('default', { month: 'long', year: 'numeric' });
    };

    const prevMonth = () => {
        setCurrentMonth(prev => {
            const newDate = new Date(prev);
            newDate.setMonth(prev.getMonth() - 1);
            return newDate;
        });
    };

    const nextMonth = () => {
        setCurrentMonth(prev => {
            const newDate = new Date(prev);
            newDate.setMonth(prev.getMonth() + 1);
            return newDate;
        });
    };

    const today = formatDateLocal(new Date());
    const weeks = renderCalendar();

    return (
        <div className="relative">
            <button
                onClick={() => setIsOpen(!isOpen)}
                className={customButtonClassName || "inline-flex items-center gap-2 px-4 py-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 text-sm font-medium text-gray-700 dark:text-gray-200"}
            >
                {customButtonText === "" ? (
                    <>
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z" />
                        </svg>
                        <span>Custom Range</span>
                        <svg className={`w-4 h-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                    </>
                ) : (
                    customButtonText || getDisplayText()
                )}
            </button>

            {isOpen && (
                <div className="absolute top-full left-0 mt-2 w-96 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50">
                    <div className="p-4">
                        <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-4">
                            Select Date Range
                        </h3>

                        {/* Month navigation */}
                        <div className="flex justify-between items-center mb-4">
                            <button
                                onClick={prevMonth}
                                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full"
                            >
                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                                </svg>
                            </button>
                            <h4 className="text-lg font-medium text-gray-800 dark:text-gray-200">
                                {formatMonthYear(currentMonth)}
                            </h4>
                            <button
                                onClick={nextMonth}
                                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full"
                            >
                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                </svg>
                            </button>
                        </div>

                        {/* Calendar */}
                        <div className="mb-4">
                            {/* Day headers */}
                            <div className="grid grid-cols-7 mb-1">
                                {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day) => (
                                    <div
                                        key={day}
                                        className="text-center text-xs font-medium text-gray-500 dark:text-gray-400 py-1"
                                    >
                                        {day}
                                    </div>
                                ))}
                            </div>

                            {/* Calendar grid */}
                            <div className="border-t border-l border-gray-200 dark:border-gray-700">
                                {weeks.map((week, weekIndex) => (
                                    <div key={weekIndex} className="grid grid-cols-7">
                                        {week.map((dateStr) => {
                                            const isCurrentMonth = dateStr.startsWith(`${currentMonth.getFullYear()}-${String(currentMonth.getMonth() + 1).padStart(2, '0')}`);
                                            const isToday = dateStr === today;
                                            const selected = isDateSelected(dateStr);
                                            const isAvailable = isDateAvailable(dateStr);
                                            const isRangeStart = dateStr === tempStartDate;
                                            const isRangeEnd = dateStr === tempEndDate;

                                            return (
                                                <div
                                                    key={dateStr}
                                                    onClick={() => isCurrentMonth && handleDateClick(dateStr)}
                                                    onMouseEnter={() => isCurrentMonth && handleMouseEnter(dateStr)}
                                                    onMouseLeave={handleMouseLeave}
                                                    className={`
                                                        p-2 border-b border-r border-gray-200 dark:border-gray-700 text-center cursor-pointer
                                                        ${isCurrentMonth ? '' : 'opacity-30'}
                                                        ${selected ? 'bg-blue-50 dark:bg-blue-900/20' : ''}
                                                        ${isRangeStart || isRangeEnd ? 'bg-blue-500 text-white dark:bg-blue-600' : ''}
                                                        ${isToday && !selected ? 'border-2 border-blue-500 dark:border-blue-400' : ''}
                                                        ${isAvailable ? 'font-medium text-blue-600 dark:text-blue-400' : 'text-gray-400 dark:text-gray-500'}
                                                        ${isCurrentMonth && !selected ? 'hover:bg-gray-50 dark:hover:bg-gray-700' : ''}
                                                    `}
                                                >
                                                    <span className={`text-sm ${isToday && !isRangeStart && !isRangeEnd ? 'font-bold' : ''}`}>
                                                        {parseInt(dateStr.split('-')[2], 10)}
                                                    </span>
                                                </div>
                                            );
                                        })}
                                    </div>
                                ))}
                            </div>
                        </div>

                        {tempStartDate && (
                            <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                                <p className="text-sm text-blue-700 dark:text-blue-300">
                                    Selected: <strong>{tempStartDate}</strong>
                                    {tempEndDate && (
                                        <>
                                            {" "}to <strong>{tempEndDate}</strong>
                                        </>
                                    )}
                                </p>
                            </div>
                        )}

                        {/* Actions */}
                        <div className="flex gap-2">
                            <button
                                onClick={handleApply}
                                disabled={!tempStartDate}
                                className="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded-md font-medium text-sm"
                            >
                                Apply
                            </button>
                            <button
                                onClick={handleClear}
                                className="px-4 py-2 bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-200 rounded-md font-medium text-sm"
                            >
                                Clear
                            </button>
                            <button
                                onClick={() => setIsOpen(false)}
                                className="px-4 py-2 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-md font-medium text-sm"
                            >
                                Cancel
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {/* Backdrop to close dropdown */}
            {isOpen && (
                <div
                    className="fixed inset-0 z-40"
                    onClick={() => setIsOpen(false)}
                />
            )}
        </div>
    );
}; 