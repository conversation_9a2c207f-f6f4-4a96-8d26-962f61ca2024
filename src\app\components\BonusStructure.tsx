import React from 'react';
import { clsx } from 'clsx';
import { convertDecimalMinutesToTime } from '../lib/parseNewXLSX';
import { useDepartment, DepartmentTargets } from './DepartmentContext';

interface BonusStructureProps {
    data?: Array<{
        name: string;
        dsat: number | null;
        aht: number | null;
        qa?: number | null; // QA percentage if available
        surveys?: number; // Survey count for weighted calculation
    }>;
    mtdTeamPerformance?: { dsat: number; aht: number; qa?: number } | null; // MTD Team Performance from MTD BI Data
}

// Function to get the QA scaling factor based on QA score
function getQAScalingFactor(qaScore: number | null): number {
    if (qaScore === null) return 0;

    // Apply the new QA scaling thresholds
    if (qaScore >= 85) return 1.0;      // 100% of bonus amount
    if (qaScore >= 80) return 0.75;     // 75% of bonus amount
    if (qaScore >= 75) return 0.5;      // 50% of bonus amount
    return 0;                           // 0% of bonus amount (less than 75%)
}

// Helper functions for bonus calculations
function getQABonus(qaScore: number | null, dsat: number | null, aht: number | null, config: DepartmentTargets): number {
    // Must have ALL three metrics to be eligible for QA bonus
    if (qaScore === null || dsat === null || aht === null) return 0;

    // Must meet department-specific target requirements for DSAT and AHT
    if (dsat > config.dsat.target || aht > config.aht.target) return 0;

    // QA must be 90% or higher for the kicker
    if (qaScore < 90) return 0;

    // Apply the QA scaling factor to the QA kicker
    const qaScalingFactor = getQAScalingFactor(qaScore);
    return 100000 * qaScalingFactor; // $100,000 kicker for 90% or higher, scaled by QA factor
}

function getDSATAHTBonus(dsat: number | null, aht: number | null, qa: number | null, config: DepartmentTargets): number {
    // Must have ALL three metrics to be eligible for performance bonus
    if (dsat === null || aht === null || qa === null) return 0;

    // Calculate bonus amount based on department-specific DSAT/AHT structure
    let bonusAmount = 0;

    // Use department-specific bonus structure
    if (dsat <= config.dsat.excellent) {
        // Excellent DSAT range
        if (aht <= config.aht.excellent) {
            bonusAmount = config.agentBonus.tier1[2].amount; // Best AHT
        } else if (aht <= config.aht.good) {
            bonusAmount = config.agentBonus.tier1[1].amount; // Good AHT
        } else if (aht <= config.aht.target) {
            bonusAmount = config.agentBonus.tier1[0].amount; // Target AHT
        }
    } else if (dsat <= config.dsat.good) {
        // Good DSAT range
        if (aht <= config.aht.excellent) {
            bonusAmount = config.agentBonus.tier2[2].amount; // Best AHT
        } else if (aht <= config.aht.good) {
            bonusAmount = config.agentBonus.tier2[1].amount; // Good AHT
        } else if (aht <= config.aht.target) {
            bonusAmount = config.agentBonus.tier2[0].amount; // Target AHT
        }
    } else if (dsat <= config.dsat.target) {
        // Target DSAT range
        if (aht <= config.aht.excellent) {
            bonusAmount = config.agentBonus.tier3[2].amount; // Best AHT
        } else if (aht <= config.aht.good) {
            bonusAmount = config.agentBonus.tier3[1].amount; // Good AHT
        } else if (aht <= config.aht.target) {
            bonusAmount = config.agentBonus.tier3[0].amount; // Target AHT
        }
    }

    // Apply QA scaling factor to the calculated bonus amount
    const qaScalingFactor = getQAScalingFactor(qa);
    return bonusAmount * qaScalingFactor;
}

function formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
    }).format(amount);
}

// Supervisor bonus calculation based on team performance using new scorecard system
function getSupervisorBonus(teamDSAT: number, teamAHT: number, teamQA: number, config: DepartmentTargets, data: Array<{ name: string; dsat: number | null; aht: number | null; qa?: number | null }>): number {
    // Use new scorecard system if available
    if (config.supervisorBonus.scorecardPayout && config.supervisorBonus.scorecardPayout.length > 0) {
        return getSupervisorScorecardBonus(teamDSAT, teamAHT, teamQA, config, data);
    }

    // Fallback to legacy tier system
    let bonusAmount = 0;

    // Check each tier in order (tier1 = best performance, highest bonus)
    for (const tier of config.supervisorBonus.tier1) {
        if (teamDSAT <= tier.dsat && teamAHT <= tier.aht) {
            bonusAmount = tier.amount;
            break;
        }
    }

    if (bonusAmount === 0) {
        for (const tier of config.supervisorBonus.tier2) {
            if (teamDSAT <= tier.dsat && teamAHT <= tier.aht) {
                bonusAmount = tier.amount;
                break;
            }
        }
    }

    if (bonusAmount === 0) {
        for (const tier of config.supervisorBonus.tier3) {
            if (teamDSAT <= tier.dsat && teamAHT <= tier.aht) {
                bonusAmount = tier.amount;
                break;
            }
        }
    }

    // Apply QA scaling factor to the calculated bonus amount
    const qaScalingFactor = getQAScalingFactor(teamQA);
    return bonusAmount * qaScalingFactor;
}

// New supervisor scorecard bonus calculation
function getSupervisorScorecardBonus(teamDSAT: number, teamAHT: number, teamQA: number, config: DepartmentTargets, data: Array<{ name: string; dsat: number | null; aht: number | null; qa?: number | null }>): number {
    // Check eligibility requirements first
    if (!checkSupervisorEligibility(teamDSAT, teamAHT, config)) {
        return 0;
    }

    // Calculate scorecard percentage based on agents meeting targets
    const totalAgents = data.length;
    if (totalAgents === 0) return 0;

    // Count agents meeting DSAT target (≤ 44% for Customer Support, ≤ 42% for Inbound)
    const dsatTargetAgents = data.filter(agent =>
        agent.dsat !== null && agent.dsat <= config.dsat.target
    ).length;

    // Count agents meeting AHT target (≤ 10:50 for Customer Support, ≤ 11:00 for Inbound)
    const ahtTargetAgents = data.filter(agent =>
        agent.aht !== null && agent.aht <= config.aht.target
    ).length;

    // Count agents meeting QA target (≥ 85% for supervisors)
    const qaTargetAgents = data.filter(agent =>
        agent.qa !== null && agent.qa !== undefined && agent.qa >= 85
    ).length;

    // Calculate weighted scorecard percentage
    const dsatPercentage = (dsatTargetAgents / totalAgents) * 100;
    const ahtPercentage = (ahtTargetAgents / totalAgents) * 100;
    const qaPercentage = (qaTargetAgents / totalAgents) * 100;

    const scorecardScore =
        (dsatPercentage * config.supervisorBonus.scorecardWeights.dsat.weight / 100) +
        (ahtPercentage * config.supervisorBonus.scorecardWeights.aht.weight / 100) +
        (qaPercentage * config.supervisorBonus.scorecardWeights.vpi.weight / 100);

    // Find matching payout tier
    for (const tier of config.supervisorBonus.scorecardPayout) {
        if (scorecardScore >= tier.minScore && scorecardScore <= tier.maxScore) {
            return tier.amount;
        }
    }

    return 0;
}

// Calculate supervisor scorecard breakdown for display
function calculateSupervisorScorecard(teamDSAT: number, teamAHT: number, teamQA: number, config: DepartmentTargets, data: Array<{ name: string; dsat: number | null; aht: number | null; qa?: number | null }>) {
    const totalAgents = data.length;

    if (totalAgents === 0) {
        return {
            dsatPercentage: 0,
            ahtPercentage: 0,
            qaPercentage: 0,
            scorecardScore: 0,
            isEligible: false,
            dsatTargetAgents: 0,
            ahtTargetAgents: 0,
            qaTargetAgents: 0,
            totalAgents: 0
        };
    }

    // Count agents meeting targets
    const dsatTargetAgents = data.filter(agent =>
        agent.dsat !== null && agent.dsat <= config.dsat.target
    ).length;

    const ahtTargetAgents = data.filter(agent =>
        agent.aht !== null && agent.aht <= config.aht.target
    ).length;

    const qaTargetAgents = data.filter(agent =>
        agent.qa !== null && agent.qa !== undefined && agent.qa >= 85
    ).length;

    // Calculate percentages
    const dsatPercentage = (dsatTargetAgents / totalAgents) * 100;
    const ahtPercentage = (ahtTargetAgents / totalAgents) * 100;
    const qaPercentage = (qaTargetAgents / totalAgents) * 100;

    // Calculate weighted scorecard score
    const scorecardScore =
        (dsatPercentage * config.supervisorBonus.scorecardWeights.dsat.weight / 100) +
        (ahtPercentage * config.supervisorBonus.scorecardWeights.aht.weight / 100) +
        (qaPercentage * config.supervisorBonus.scorecardWeights.vpi.weight / 100);

    // Check eligibility
    const isEligible = checkSupervisorEligibility(teamDSAT, teamAHT, config);

    return {
        dsatPercentage,
        ahtPercentage,
        qaPercentage,
        scorecardScore,
        isEligible,
        dsatTargetAgents,
        ahtTargetAgents,
        qaTargetAgents,
        totalAgents
    };
}

// Check supervisor eligibility based on new requirements
function checkSupervisorEligibility(teamDSAT: number, teamAHT: number, config: DepartmentTargets): boolean {
    // Check if team DSAT is at or below the department's agent DSAT target
    // Customer Support: 44%, Inbound Concierge: 42%
    if (teamDSAT > config.dsat.target) {
        return false;
    }

    // Check if monthly AHT is 10:50 or less (10.83 minutes)
    if (teamAHT > 10.83) {
        return false;
    }

    return true;
}



// Calculate team recognition bonus for agents
function getTeamRecognitionAgentBonus(teamDSAT: number, teamQA: number, teamAHT: number, config: DepartmentTargets): number {
    // Check if team meets the minimum requirements for AHT
    if (teamAHT > 11) {
        return 0;
    }

    // Check if team DSAT is below or equal to the goal
    if (teamDSAT <= config.teamRecognition.agentGoal.dsat) {
        // Apply QA scaling factor to team recognition bonus
        const qaScalingFactor = getQAScalingFactor(teamQA);
        return config.teamRecognition.agentGoal.amount * qaScalingFactor;
    }

    return 0;
}

// Check if an individual agent is eligible for the team recognition bonus
function isAgentEligibleForTeamRecognition(agent: { dsat: number | null; aht: number | null; qa?: number | null }, config: DepartmentTargets): boolean {
    // Agent must have valid metrics
    if (agent.dsat === null || agent.aht === null || agent.qa == null) {
        return false;
    }

    // Agent must be below the DSAT target threshold
    if (agent.dsat > config.dsat.target) {
        return false;
    }

    // Agent must meet AHT target
    if (agent.aht > config.aht.target) {
        return false;
    }

    // Agent must have at least 70% QA to be eligible for any portion of team recognition
    if (agent.qa < 70) {
        return false;
    }

    return true;
}

// Calculate team recognition bonus for supervisors
function getTeamRecognitionSupervisorBonus(teamDSAT: number, teamQA: number, teamAHT: number, config: DepartmentTargets): number {
    // Check if team meets the minimum AHT requirement
    if (teamAHT > 11) {
        return 0;
    }

    // Calculate base bonus amount based on DSAT goals
    let bonusAmount = 0;

    // Check each tier in order (best to worst)
    for (const goal of config.teamRecognition.supervisorGoals) {
        if (teamDSAT <= goal.dsat) {
            bonusAmount = goal.amount;
            break;
        }
    }

    // Apply QA scaling factor
    const qaScalingFactor = getQAScalingFactor(teamQA);
    return bonusAmount * qaScalingFactor;
}

export const BonusStructure: React.FC<BonusStructureProps> = ({ data = [], mtdTeamPerformance }) => {
    const { config } = useDepartment();

    // Team Performance calculations - use MTD team performance when available, otherwise calculate from agent data
    const teamPerformanceData = React.useMemo(() => {
        // Use MTD team performance data if available
        if (mtdTeamPerformance) {
            const supervisorBonus = getSupervisorBonus(
                mtdTeamPerformance.dsat,
                mtdTeamPerformance.aht,
                mtdTeamPerformance.qa || 0,
                config,
                data
            );

            return {
                dsat: mtdTeamPerformance.dsat,
                aht: mtdTeamPerformance.aht,
                qa: mtdTeamPerformance.qa || 0,
                supervisorBonus
            };
        }

        // Fallback to calculating from individual agent data if no MTD team performance
        if (data.length === 0) return { dsat: 0, aht: 0, qa: 0, supervisorBonus: 0 };

        // Calculate survey-weighted team DSAT if survey data is available
        let teamDSAT = 0;
        const hasSurveyData = data.some(agent => agent.surveys && agent.surveys > 0);

        if (hasSurveyData) {
            // Calculate survey-weighted team DSAT
            let totalWeightedScore = 0;
            let totalSurveys = 0;

            const agentsWithSurveys = data.filter(agent =>
                agent.dsat != null && agent.surveys && agent.surveys > 0
            );

            agentsWithSurveys.forEach(agent => {
                if (agent.dsat !== null && agent.surveys) {
                    totalWeightedScore += agent.dsat * agent.surveys;
                    totalSurveys += agent.surveys;
                }
            });

            teamDSAT = totalSurveys > 0 ? totalWeightedScore / totalSurveys : 0;
        } else {
            // Fallback to simple average
            const validDSAT = data.filter(agent => agent.dsat != null).map(agent => agent.dsat!);
            teamDSAT = validDSAT.length > 0
                ? validDSAT.reduce((sum, dsat) => sum + dsat, 0) / validDSAT.length
                : 0;
        }

        const validAHT = data.filter(agent => agent.aht != null).map(agent => agent.aht!);
        const teamAHT = validAHT.length > 0
            ? validAHT.reduce((sum, aht) => sum + aht, 0) / validAHT.length
            : 0;

        const validQA = data.filter(agent => agent.qa != null).map(agent => agent.qa!);
        const teamQA = validQA.length > 0
            ? validQA.reduce((sum, qa) => sum + qa, 0) / validQA.length
            : 0;

        const supervisorBonus = getSupervisorBonus(teamDSAT, teamAHT, teamQA, config, data);

        return {
            dsat: teamDSAT,
            aht: teamAHT,
            qa: teamQA,
            supervisorBonus
        };
    }, [data, config, mtdTeamPerformance]);

    // Calculate team recognition bonuses
    const teamRecognitionBonuses = React.useMemo(() => {
        if (data.length === 0) return { agentBonus: 0, supervisorBonus: 0, isEligible: false };

        const { dsat, aht, qa } = teamPerformanceData;

        const agentBonus = getTeamRecognitionAgentBonus(dsat, qa, aht, config);
        const supervisorBonus = getTeamRecognitionSupervisorBonus(dsat, qa, aht, config);
        const isEligible = qa >= 80 && aht <= 11;

        return {
            agentBonus,
            supervisorBonus,
            isEligible
        };
    }, [data, teamPerformanceData, config]);

    // Calculate supervisor scorecard breakdown if using new system
    const supervisorScorecard = React.useMemo(() => {
        if (data.length === 0 || !config.supervisorBonus.scorecardPayout || config.supervisorBonus.scorecardPayout.length === 0) {
            return null;
        }

        return calculateSupervisorScorecard(
            teamPerformanceData.dsat,
            teamPerformanceData.aht,
            teamPerformanceData.qa,
            config,
            data
        );
    }, [data, teamPerformanceData, config]);

    // Calculate statistics if data is provided
    const stats = React.useMemo(() => {
        if (data.length === 0) return null;

        const qaEligible = data.filter(agent => (agent.qa || 0) >= 90).length;
        const dsatAhtBonusEligible = data.filter(agent => {
            return getDSATAHTBonus(agent.dsat, agent.aht, agent.qa ?? null, config) > 0;
        }).length;

        const totalQABonus = data.reduce((sum, agent) => sum + getQABonus(agent.qa ?? null, agent.dsat, agent.aht, config), 0);
        const totalDSATAHTBonus = data.reduce((sum, agent) => sum + getDSATAHTBonus(agent.dsat, agent.aht, agent.qa ?? null, config), 0);

        // Include team recognition bonus in stats if eligible, but only for qualifying agents
        const eligibleAgentCount = data.filter(agent => isAgentEligibleForTeamRecognition(agent, config)).length;
        const teamRecognitionTotal = teamRecognitionBonuses.isEligible ?
            (teamRecognitionBonuses.agentBonus * eligibleAgentCount) : 0;

        return {
            qaEligible,
            dsatAhtBonusEligible,
            totalQABonus,
            totalDSATAHTBonus,
            teamRecognitionTotal,
            totalBonus: totalQABonus + totalDSATAHTBonus + teamRecognitionTotal
        };
    }, [data, config, teamRecognitionBonuses]);

    return (
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700">
            {/* Header */}
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                    <div>
                        <h3 className="text-2xl font-bold text-gray-800 dark:text-gray-200">
                            💰 Bonus Structure
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                            Performance-based incentive program with QA and combined DSAT/AHT bonuses
                        </p>
                    </div>

                    {/* Stats if data is available */}
                    {stats && (
                        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 text-sm">
                            <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-3 border border-purple-200 dark:border-purple-700">
                                <div className="text-lg font-bold text-purple-600 dark:text-purple-400">{stats.qaEligible}</div>
                                <div className="text-purple-600 dark:text-purple-400">QA Eligible</div>
                            </div>
                            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3 border border-blue-200 dark:border-blue-700">
                                <div className="text-lg font-bold text-blue-600 dark:text-blue-400">{stats.dsatAhtBonusEligible}</div>
                                <div className="text-blue-600 dark:text-blue-400">Performance Bonus</div>
                            </div>
                            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-3 border border-green-200 dark:border-green-700">
                                <div className="text-lg font-bold text-green-600 dark:text-green-400">{formatCurrency(stats.totalQABonus)}</div>
                                <div className="text-green-600 dark:text-green-400">QA Bonuses</div>
                            </div>
                            <div className="bg-amber-50 dark:bg-amber-900/20 rounded-lg p-3 border border-amber-200 dark:border-amber-700">
                                <div className="text-lg font-bold text-amber-600 dark:text-amber-400">
                                    {formatCurrency(stats.totalDSATAHTBonus + (teamRecognitionBonuses.isEligible ? stats.teamRecognitionTotal : 0))}
                                </div>
                                <div className="text-amber-600 dark:text-amber-400">
                                    Performance Bonuses
                                    {teamRecognitionBonuses.isEligible && stats.teamRecognitionTotal > 0 && (
                                        <span className="block text-xs">
                                            (Includes Team Recognition)
                                        </span>
                                    )}
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Content */}
            <div className="p-6 space-y-8">
                {/* Individual Agent Bonus Breakdown */}
                {data.length > 0 && (
                    <div>
                        <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4 flex items-center gap-2">
                            🏆 Individual Bonus Winners
                        </h4>
                        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden">
                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    <thead className="bg-gray-50 dark:bg-gray-900">
                                        <tr>
                                            <th className="px-6 py-4 text-left text-xs font-bold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                                                Agent
                                            </th>
                                            <th className="px-4 py-4 text-center text-xs font-bold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                                                DSAT
                                            </th>
                                            <th className="px-4 py-4 text-center text-xs font-bold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                                                AHT
                                            </th>
                                            <th className="px-4 py-4 text-center text-xs font-bold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                                                QA
                                            </th>
                                            <th className="px-4 py-4 text-center text-xs font-bold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                                                QA Kicker
                                            </th>
                                            <th className="px-4 py-4 text-center text-xs font-bold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                                                Performance Bonus
                                            </th>
                                            <th className="px-4 py-4 text-center text-xs font-bold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                                                Team Recognition
                                            </th>
                                            <th className="px-4 py-4 text-center text-xs font-bold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                                                Total Bonus
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                                        {data
                                            .filter(agent =>
                                                getQABonus(agent.qa ?? null, agent.dsat, agent.aht ?? null, config) > 0 ||
                                                getDSATAHTBonus(agent.dsat, agent.aht ?? null, agent.qa ?? null, config) > 0 ||
                                                (teamRecognitionBonuses.isEligible && teamRecognitionBonuses.agentBonus > 0 && isAgentEligibleForTeamRecognition(agent, config))
                                            )
                                            .sort((a, b) => {
                                                const aEligibleForTeamBonus = isAgentEligibleForTeamRecognition(a, config);
                                                const bEligibleForTeamBonus = isAgentEligibleForTeamRecognition(b, config);

                                                const aTeamBonus = teamRecognitionBonuses.isEligible && aEligibleForTeamBonus ? teamRecognitionBonuses.agentBonus : 0;
                                                const bTeamBonus = teamRecognitionBonuses.isEligible && bEligibleForTeamBonus ? teamRecognitionBonuses.agentBonus : 0;

                                                const totalA = getQABonus(a.qa ?? null, a.dsat, a.aht ?? null, config) +
                                                    getDSATAHTBonus(a.dsat, a.aht ?? null, a.qa ?? null, config) + aTeamBonus;
                                                const totalB = getQABonus(b.qa ?? null, b.dsat, b.aht ?? null, config) +
                                                    getDSATAHTBonus(b.dsat, b.aht ?? null, b.qa ?? null, config) + bTeamBonus;
                                                return totalB - totalA; // Sort by total bonus descending
                                            })
                                            .map((agent) => {
                                                const qaBonus = getQABonus(agent.qa ?? null, agent.dsat, agent.aht ?? null, config);
                                                const performanceBonus = getDSATAHTBonus(agent.dsat, agent.aht ?? null, agent.qa ?? null, config);
                                                const isEligibleForTeamBonus = isAgentEligibleForTeamRecognition(agent, config);
                                                const teamBonus = teamRecognitionBonuses.isEligible && isEligibleForTeamBonus ? teamRecognitionBonuses.agentBonus : 0;
                                                const totalBonus = qaBonus + performanceBonus + teamBonus;

                                                return (
                                                    <tr key={agent.name} className="hover:bg-gray-50 dark:hover:bg-gray-900/50">
                                                        <td className="px-6 py-4 whitespace-nowrap">
                                                            <div className="flex items-center">
                                                                <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-bold text-sm mr-3">
                                                                    {agent.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                                                                </div>
                                                                <div className="font-medium text-gray-900 dark:text-gray-100">
                                                                    {agent.name}
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td className="px-4 py-4 whitespace-nowrap text-center">
                                                            <span className={clsx(
                                                                "font-semibold",
                                                                agent.dsat != null
                                                                    ? agent.dsat <= config.dsat.excellent
                                                                        ? "text-green-600 dark:text-green-400"
                                                                        : agent.dsat <= config.dsat.good
                                                                            ? "text-yellow-600 dark:text-yellow-400"
                                                                            : agent.dsat <= config.dsat.target
                                                                                ? "text-orange-600 dark:text-orange-400"
                                                                                : "text-red-600 dark:text-red-400"
                                                                    : "text-gray-400"
                                                            )}>
                                                                {agent.dsat != null ? agent.dsat.toFixed(2) + "%" : "-"}
                                                            </span>
                                                        </td>
                                                        <td className="px-4 py-4 whitespace-nowrap text-center">
                                                            <span className={clsx(
                                                                "font-semibold",
                                                                agent.aht != null
                                                                    ? agent.aht <= config.aht.excellent
                                                                        ? "text-green-600 dark:text-green-400"
                                                                        : agent.aht <= config.aht.good
                                                                            ? "text-yellow-600 dark:text-yellow-400"
                                                                            : agent.aht <= config.aht.target
                                                                                ? "text-orange-600 dark:text-orange-400"
                                                                                : "text-red-600 dark:text-red-400"
                                                                    : "text-gray-400"
                                                            )}>
                                                                {agent.aht != null ? convertDecimalMinutesToTime(agent.aht) : "-"}
                                                            </span>
                                                        </td>
                                                        <td className="px-4 py-4 whitespace-nowrap text-center">
                                                            <span className={clsx(
                                                                "font-semibold",
                                                                agent.qa != null
                                                                    ? agent.qa >= config.qa.excellent
                                                                        ? "text-green-600 dark:text-green-400"
                                                                        : agent.qa >= 85
                                                                            ? "text-yellow-600 dark:text-yellow-400"
                                                                            : "text-red-600 dark:text-red-400"
                                                                    : "text-gray-400"
                                                            )}>
                                                                {agent.qa != null ? agent.qa.toFixed(2) + "%" : "-"}
                                                            </span>
                                                        </td>
                                                        <td className="px-4 py-4 whitespace-nowrap text-center">
                                                            <span className={clsx(
                                                                "font-bold",
                                                                qaBonus > 0 ? "text-green-600 dark:text-green-400" : "text-gray-400"
                                                            )}>
                                                                {qaBonus > 0 ? formatCurrency(qaBonus) : "-"}
                                                            </span>
                                                        </td>
                                                        <td className="px-4 py-4 whitespace-nowrap text-center">
                                                            <span className={clsx(
                                                                "font-bold",
                                                                performanceBonus > 0 ? "text-blue-600 dark:text-blue-400" : "text-gray-400"
                                                            )}>
                                                                {performanceBonus > 0 ? formatCurrency(performanceBonus) : "-"}
                                                            </span>
                                                        </td>
                                                        <td className="px-4 py-4 whitespace-nowrap text-center">
                                                            <span className={clsx(
                                                                "font-bold",
                                                                teamBonus > 0 ? "text-amber-600 dark:text-amber-400" : "text-gray-400"
                                                            )}>
                                                                {teamBonus > 0 ? formatCurrency(teamBonus) : "-"}
                                                            </span>
                                                        </td>
                                                        <td className="px-4 py-4 whitespace-nowrap text-center">
                                                            <span className="font-bold text-lg text-green-600 dark:text-green-400">
                                                                {formatCurrency(totalBonus)}
                                                            </span>
                                                        </td>
                                                    </tr>
                                                );
                                            })}
                                    </tbody>
                                </table>
                            </div>
                            {data.filter(agent =>
                                getQABonus(agent.qa ?? null, agent.dsat, agent.aht ?? null, config) > 0 ||
                                getDSATAHTBonus(agent.dsat, agent.aht ?? null, agent.qa ?? null, config) > 0 ||
                                (teamRecognitionBonuses.isEligible && teamRecognitionBonuses.agentBonus > 0 && isAgentEligibleForTeamRecognition(agent, config))
                            ).length === 0 && (
                                    <div className="p-8 text-center text-gray-500 dark:text-gray-400">
                                        No agents currently qualify for bonuses based on available data.
                                    </div>
                                )}
                        </div>
                    </div>
                )}

                {/* QA Target Visual */}
                <div>
                    <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4 flex items-center gap-2">
                        🎯 QA Performance Target
                    </h4>
                    <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
                        {/* Performance Tiers Grid */}
                        <div className="grid grid-cols-5 gap-2">
                            <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-3 border border-red-200 dark:border-red-700">
                                <div className="text-center">
                                    <div className="text-xl font-bold text-red-600 dark:text-red-400 mb-1">0% - 74%</div>
                                    <div className="text-sm text-red-600 dark:text-red-400 font-medium">0% Bonus</div>
                                    <div className="text-xs text-red-500 dark:text-red-400 mt-1">No bonus eligibility</div>
                                </div>
                            </div>
                            <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-3 border border-yellow-200 dark:border-yellow-700">
                                <div className="text-center">
                                    <div className="text-xl font-bold text-yellow-600 dark:text-yellow-400 mb-1">75% - 79%</div>
                                    <div className="text-sm text-yellow-600 dark:text-yellow-400 font-medium">50% Bonus</div>
                                    <div className="text-xs text-yellow-500 dark:text-yellow-400 mt-1">Half bonus amount</div>
                                </div>
                            </div>
                            <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-3 border border-orange-200 dark:border-orange-700">
                                <div className="text-center">
                                    <div className="text-xl font-bold text-orange-600 dark:text-orange-400 mb-1">80% - 84%</div>
                                    <div className="text-sm text-orange-600 dark:text-orange-400 font-medium">75% Bonus</div>
                                    <div className="text-xs text-orange-500 dark:text-orange-400 mt-1">Three-quarter bonus</div>
                                </div>
                            </div>
                            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-3 border border-green-200 dark:border-green-700">
                                <div className="text-center">
                                    <div className="text-xl font-bold text-green-600 dark:text-green-400 mb-1">85% - 89%</div>
                                    <div className="text-sm text-green-600 dark:text-green-400 font-medium">100% Bonus</div>
                                    <div className="text-xs text-green-500 dark:text-green-400 mt-1">Full bonus amount</div>
                                </div>
                            </div>
                            <div className="bg-gradient-to-r from-green-50 to-purple-50 dark:from-green-900/20 dark:to-purple-900/20 rounded-lg p-3 border border-purple-200 dark:border-purple-700">
                                <div className="text-center">
                                    <div className="text-xl font-bold bg-gradient-to-r from-green-600 to-purple-600 bg-clip-text text-transparent mb-1">90%+</div>
                                    <div className="text-sm text-purple-600 dark:text-purple-400 font-medium">QA Kicker</div>
                                    <div className="text-xs text-purple-500 dark:text-purple-400 mt-1">+$100,000 bonus</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                        * QA Kicker requires DSAT ≤{config.dsat.target}% and AHT ≤{convertDecimalMinutesToTime(config.aht.target)}; All bonuses subject to QA scaling factor
                    </p>
                </div>

                {/* Combined DSAT/AHT Bonus Structure */}
                <div>
                    <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4 flex items-center gap-2">
                        🏆 Performance Bonus Matrix
                    </h4>
                    <div className="bg-gradient-to-r from-green-50 to-yellow-50 dark:from-green-900/10 dark:to-yellow-900/10 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden">
                        <table className="w-full">
                            <thead className="bg-gray-100 dark:bg-gray-900">
                                <tr>
                                    <th className="px-4 py-4 text-center text-sm font-bold text-gray-800 dark:text-gray-200">
                                        DSAT Range
                                    </th>
                                    <th className="px-4 py-4 text-center text-sm font-bold text-gray-800 dark:text-gray-200" colSpan={3}>
                                        AHT Bonus Amounts
                                    </th>
                                </tr>
                                <tr className="bg-gray-50 dark:bg-gray-800">
                                    <th className="px-4 py-3 text-center text-xs font-semibold text-gray-600 dark:text-gray-300 border-r border-gray-200 dark:border-gray-600">
                                        Performance Range
                                    </th>
                                    <th className="px-4 py-3 text-center text-sm font-bold text-gray-700 dark:text-gray-200">
                                        {convertDecimalMinutesToTime(config.aht.target)} - {convertDecimalMinutesToTime(config.aht.good)}
                                    </th>
                                    <th className="px-4 py-3 text-center text-sm font-bold text-gray-700 dark:text-gray-200">
                                        {convertDecimalMinutesToTime(config.aht.good)} - {convertDecimalMinutesToTime(config.aht.excellent)}
                                    </th>
                                    <th className="px-4 py-3 text-center text-sm font-bold text-gray-700 dark:text-gray-200">
                                        {convertDecimalMinutesToTime(config.aht.excellent)} or less
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                {/* Tier 3 - Highest DSAT Range */}
                                <tr className="border-b border-gray-200 dark:border-gray-700 bg-orange-50 dark:bg-orange-900/20">
                                    <td className="px-4 py-4 text-center font-bold border-r border-gray-200 dark:border-gray-600 text-orange-700 dark:text-orange-300">
                                        {config.dsat.target}% - {config.dsat.good + 0.01}%
                                    </td>
                                    <td className="px-4 py-4 text-center font-bold text-green-600 dark:text-green-400">
                                        {formatCurrency(config.agentBonus.tier3[2].amount)}
                                    </td>
                                    <td className="px-4 py-4 text-center font-bold text-green-600 dark:text-green-400">
                                        {formatCurrency(config.agentBonus.tier3[1].amount)}
                                    </td>
                                    <td className="px-4 py-4 text-center font-bold text-green-600 dark:text-green-400">
                                        {formatCurrency(config.agentBonus.tier3[0].amount)}
                                    </td>
                                </tr>
                                {/* Tier 2 - Middle DSAT Range */}
                                <tr className="border-b border-gray-200 dark:border-gray-700 bg-yellow-50 dark:bg-yellow-900/20">
                                    <td className="px-4 py-4 text-center font-bold border-r border-gray-200 dark:border-gray-600 text-yellow-700 dark:text-yellow-300">
                                        {config.dsat.good}% - {config.dsat.excellent + 0.01}%
                                    </td>
                                    <td className="px-4 py-4 text-center font-bold text-green-600 dark:text-green-400">
                                        {formatCurrency(config.agentBonus.tier2[2].amount)}
                                    </td>
                                    <td className="px-4 py-4 text-center font-bold text-green-600 dark:text-green-400">
                                        {formatCurrency(config.agentBonus.tier2[1].amount)}
                                    </td>
                                    <td className="px-4 py-4 text-center font-bold text-green-600 dark:text-green-400">
                                        {formatCurrency(config.agentBonus.tier2[0].amount)}
                                    </td>
                                </tr>
                                {/* Tier 1 - Best DSAT Range */}
                                <tr className="bg-green-50 dark:bg-green-900/20">
                                    <td className="px-4 py-4 text-center font-bold border-r border-gray-200 dark:border-gray-600 text-green-700 dark:text-green-300">
                                        {config.dsat.excellent}% or less
                                    </td>
                                    <td className="px-4 py-4 text-center font-bold text-green-600 dark:text-green-400">
                                        {formatCurrency(config.agentBonus.tier1[2].amount)}
                                    </td>
                                    <td className="px-4 py-4 text-center font-bold text-green-600 dark:text-green-400">
                                        {formatCurrency(config.agentBonus.tier1[1].amount)}
                                    </td>
                                    <td className="px-4 py-4 text-center font-bold text-green-600 dark:text-green-400">
                                        {formatCurrency(config.agentBonus.tier1[0].amount)}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                        * All amounts are subject to QA scaling factor. Requires all three metrics (DSAT ≤{config.dsat.target}%, AHT ≤{convertDecimalMinutesToTime(config.aht.target)}, QA ≥85%) to be eligible.
                    </p>
                </div>

                {/* Team Recognition Bonus */}
                {data.length > 0 && (
                    <div>
                        <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4 flex items-center gap-2">
                            🏅 Team Building Recognition
                        </h4>
                        <div className="bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-900/20 dark:to-yellow-900/20 rounded-xl border border-amber-200 dark:border-amber-700 p-6">
                            <div className="mb-4">
                                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                                    Additional recognition for teams that achieve group DSAT goals. Requires team QA ≥80% and AHT ≤11min.
                                    <span className="block mt-1 font-medium">Only agents who already qualify for individual bonuses are eligible for team recognition.</span>
                                </p>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                {/* Agent Team Recognition */}
                                <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                                    <div className="bg-blue-50 dark:bg-blue-900/20 px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <h5 className="font-bold text-blue-700 dark:text-blue-300">Agent Team Recognition</h5>
                                    </div>
                                    <div className="p-4">
                                        <div className="overflow-hidden rounded-lg border border-gray-200 dark:border-gray-600">
                                            <table className="w-full">
                                                <thead className="bg-gray-50 dark:bg-gray-900">
                                                    <tr>
                                                        <th className="px-4 py-3 text-left text-xs font-semibold text-gray-600 dark:text-gray-300">Goal</th>
                                                        <th className="px-4 py-3 text-center text-xs font-semibold text-gray-600 dark:text-gray-300">Award</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr className={clsx(
                                                        "border-b border-gray-200 dark:border-gray-700",
                                                        teamPerformanceData.dsat <= config.teamRecognition.agentGoal.dsat ? "bg-green-50 dark:bg-green-900/20" : ""
                                                    )}>
                                                        <td className="px-4 py-3 text-gray-700 dark:text-gray-300 font-medium">
                                                            {"<"}{config.teamRecognition.agentGoal.dsat}% DSAT
                                                        </td>
                                                        <td className="px-4 py-3 text-center font-bold text-green-600 dark:text-green-400">
                                                            {formatCurrency(config.teamRecognition.agentGoal.amount)}
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>

                                        <div className="mt-4 flex items-center justify-between">
                                            <span className="text-sm text-gray-600 dark:text-gray-400">Status:</span>
                                            {!teamRecognitionBonuses.isEligible ? (
                                                <span className="text-sm font-medium text-red-600 dark:text-red-400">Not Eligible (QA or AHT requirements not met)</span>
                                            ) : teamRecognitionBonuses.agentBonus > 0 ? (
                                                <span className="text-sm font-medium text-green-600 dark:text-green-400">Eligible - Goal Achieved!</span>
                                            ) : (
                                                <span className="text-sm font-medium text-amber-600 dark:text-amber-400">Eligible - Goal Not Yet Achieved</span>
                                            )}
                                        </div>
                                    </div>
                                </div>

                                {/* Supervisor Team Recognition */}
                                <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                                    <div className="bg-purple-50 dark:bg-purple-900/20 px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                        <h5 className="font-bold text-purple-700 dark:text-purple-300">Supervisor Team Recognition</h5>
                                    </div>
                                    <div className="p-4">
                                        <div className="overflow-hidden rounded-lg border border-gray-200 dark:border-gray-600">
                                            <table className="w-full">
                                                <thead className="bg-gray-50 dark:bg-gray-900">
                                                    <tr>
                                                        <th className="px-4 py-3 text-left text-xs font-semibold text-gray-600 dark:text-gray-300">Goal (DSAT)</th>
                                                        <th className="px-4 py-3 text-center text-xs font-semibold text-gray-600 dark:text-gray-300">Award</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {config.teamRecognition.supervisorGoals.map((goal, index) => (
                                                        <tr key={index} className={clsx(
                                                            index < config.teamRecognition.supervisorGoals.length - 1 && "border-b border-gray-200 dark:border-gray-700",
                                                            teamPerformanceData.dsat <= goal.dsat ? "bg-green-50 dark:bg-green-900/20" : ""
                                                        )}>
                                                            <td className="px-4 py-3 text-gray-700 dark:text-gray-300 font-medium">
                                                                {"<"}{goal.dsat}%
                                                            </td>
                                                            <td className="px-4 py-3 text-center font-bold text-green-600 dark:text-green-400">
                                                                {formatCurrency(goal.amount)}
                                                            </td>
                                                        </tr>
                                                    ))}
                                                </tbody>
                                            </table>
                                        </div>

                                        <div className="mt-4 flex items-center justify-between">
                                            <span className="text-sm text-gray-600 dark:text-gray-400">Status:</span>
                                            {!teamRecognitionBonuses.isEligible ? (
                                                <span className="text-sm font-medium text-red-600 dark:text-red-400">Not Eligible (QA or AHT requirements not met)</span>
                                            ) : teamRecognitionBonuses.supervisorBonus > 0 ? (
                                                <span className="text-sm font-medium text-green-600 dark:text-green-400">Eligible - Goal Achieved! ({formatCurrency(teamRecognitionBonuses.supervisorBonus)})</span>
                                            ) : (
                                                <span className="text-sm font-medium text-amber-600 dark:text-amber-400">Eligible - Goal Not Yet Achieved</span>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Eligibility Summary */}
                {data.length > 0 && (
                    <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-700">
                        <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">
                            📊 Current Team Eligibility
                        </h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <div className="flex justify-between">
                                    <span className="text-gray-600 dark:text-gray-400">QA Kicker Eligible:</span>
                                    <span className="font-bold text-purple-600 dark:text-purple-400">
                                        {stats?.qaEligible} agents
                                    </span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600 dark:text-gray-400">Performance Bonus Eligible:</span>
                                    <span className="font-bold text-blue-600 dark:text-blue-400">
                                        {stats?.dsatAhtBonusEligible} agents
                                    </span>
                                </div>
                            </div>
                            <div className="space-y-2">
                                <div className="flex justify-between">
                                    <span className="text-gray-600 dark:text-gray-400">Total QA Bonuses:</span>
                                    <span className="font-bold text-green-600 dark:text-green-400">
                                        {formatCurrency(stats?.totalQABonus || 0)}
                                    </span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600 dark:text-gray-400">Total Performance Bonuses:</span>
                                    <span className="font-bold text-amber-600 dark:text-amber-400">
                                        {formatCurrency(stats?.totalDSATAHTBonus || 0)}
                                    </span>
                                </div>
                                {teamRecognitionBonuses.isEligible && teamRecognitionBonuses.agentBonus > 0 && (
                                    <div className="flex justify-between">
                                        <span className="text-gray-600 dark:text-gray-400">Team Recognition Bonuses:</span>
                                        <span className="font-bold text-amber-600 dark:text-amber-400">
                                            {formatCurrency(stats?.teamRecognitionTotal || 0)}
                                        </span>
                                    </div>
                                )}
                                <div className="flex justify-between pt-2 border-t border-gray-300 dark:border-gray-600">
                                    <span className="font-semibold text-gray-800 dark:text-gray-200">Grand Total:</span>
                                    <span className="font-bold text-xl text-green-600 dark:text-green-400">
                                        {formatCurrency(stats?.totalBonus || 0)}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Team Performance Supervisor Bonus */}
                {data.length > 0 && (
                    <div>
                        <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4 flex items-center gap-2">
                            👑 Team Performance
                        </h4>
                        <div className="bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 rounded-xl border border-purple-200 dark:border-purple-700 p-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                                <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                                    <div className="text-center">
                                        <div className="text-lg font-bold text-purple-600 dark:text-purple-400 mb-1">
                                            {teamPerformanceData.dsat.toFixed(2)}%
                                        </div>
                                        <div className="text-sm text-purple-600 dark:text-purple-400">Team DSAT</div>
                                    </div>
                                </div>
                                <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                                    <div className="text-center">
                                        <div className="text-lg font-bold text-purple-600 dark:text-purple-400 mb-1">
                                            {convertDecimalMinutesToTime(teamPerformanceData.aht)}
                                        </div>
                                        <div className="text-sm text-purple-600 dark:text-purple-400">Team AHT</div>
                                    </div>
                                </div>
                                <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                                    <div className="text-center">
                                        <div className="text-lg font-bold text-purple-600 dark:text-purple-400 mb-1">
                                            {teamPerformanceData.qa.toFixed(2)}%
                                        </div>
                                        <div className="text-sm text-purple-600 dark:text-purple-400">Team QA</div>
                                    </div>
                                </div>
                                <div className="bg-gradient-to-r from-purple-100 to-blue-100 dark:from-purple-900/40 dark:to-blue-900/40 rounded-lg p-4 border border-purple-300 dark:border-purple-600">
                                    <div className="text-center">
                                        <div className="text-xl font-bold text-purple-700 dark:text-purple-300 mb-1">
                                            {(teamPerformanceData.supervisorBonus > 0 || (teamRecognitionBonuses.isEligible && teamRecognitionBonuses.supervisorBonus > 0))
                                                ? formatCurrency(teamPerformanceData.supervisorBonus + (teamRecognitionBonuses.isEligible ? teamRecognitionBonuses.supervisorBonus : 0))
                                                : 'No Bonus'}
                                        </div>
                                        <div className="text-sm text-purple-600 dark:text-purple-400 font-medium">Supervisor Bonus</div>
                                        {teamRecognitionBonuses.isEligible && teamRecognitionBonuses.supervisorBonus > 0 && (
                                            <div className="text-xs text-amber-600 dark:text-amber-400 mt-1">
                                                Includes Team Recognition: {formatCurrency(teamRecognitionBonuses.supervisorBonus)}
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                            <div className="text-center text-sm text-purple-600 dark:text-purple-400">
                                {mtdTeamPerformance
                                    ? "📊 Based on MTD team performance data and supervisor bonus structure"
                                    : "⚠️ Based on calculated team average performance and supervisor bonus structure"
                                }
                            </div>
                        </div>
                    </div>
                )}

                {/* Supervisor Scorecard */}
                {supervisorScorecard && (
                    <div>
                        <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2 flex items-center gap-2">
                            📊 Supervisor Scorecard
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                            Percentage of agents meeting their individual targets for each metric
                        </p>
                        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-4">
                            <div className="grid grid-cols-1 lg:grid-cols-5 gap-3 items-center">
                                {/* Overall Score */}
                                <div className="text-center lg:col-span-1">
                                    <div className={`text-3xl font-bold ${supervisorScorecard.isEligible && supervisorScorecard.scorecardScore >= 55
                                        ? supervisorScorecard.scorecardScore >= 85
                                            ? "text-green-600 dark:text-green-400"
                                            : supervisorScorecard.scorecardScore >= 70
                                                ? "text-yellow-600 dark:text-yellow-400"
                                                : "text-orange-600 dark:text-orange-400"
                                        : "text-red-600 dark:text-red-400"
                                        }`}>
                                        {supervisorScorecard.scorecardScore.toFixed(1)}%
                                    </div>
                                    <div className="text-xs text-gray-600 dark:text-gray-400">Overall Score</div>
                                    <div className={`text-xs ${supervisorScorecard.isEligible
                                        ? "text-green-600 dark:text-green-400"
                                        : "text-red-600 dark:text-red-400"
                                        }`}>
                                        {supervisorScorecard.isEligible ? "✅ Eligible" : "❌ Not Eligible"}
                                    </div>
                                    {/* Eligibility Reason */}
                                    {!supervisorScorecard.isEligible && (
                                        <div className="text-xs text-red-500 dark:text-red-400 mt-1 leading-tight">
                                            {teamPerformanceData.dsat > config.dsat.target && teamPerformanceData.aht > 10.83
                                                ? `Team DSAT > ${config.dsat.target}% & AHT > 10:50`
                                                : teamPerformanceData.dsat > config.dsat.target
                                                    ? `Team DSAT > ${config.dsat.target}%`
                                                    : teamPerformanceData.aht > 10.83
                                                        ? "Team AHT > 10:50"
                                                        : "Below minimum score"
                                            }
                                        </div>
                                    )}
                                </div>

                                {/* Breakdown */}
                                <div className="lg:col-span-3">
                                    <div className="grid grid-cols-3 gap-2">
                                        <div className="bg-blue-50 dark:bg-blue-900/20 rounded p-2 border border-blue-200 dark:border-blue-700"
                                            title={`DSAT Score: ${supervisorScorecard.dsatPercentage.toFixed(1)}% (${supervisorScorecard.dsatTargetAgents} out of ${supervisorScorecard.totalAgents} agents meeting ≤${config.dsat.target}% target). Weight: 50% of total score.`}>
                                            <div className="text-center">
                                                <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
                                                    {supervisorScorecard.dsatPercentage.toFixed(0)}%
                                                </div>
                                                <div className="text-xs text-blue-600 dark:text-blue-400">DSAT (50%)</div>
                                                <div className="text-xs text-blue-500 dark:text-blue-400">
                                                    {supervisorScorecard.dsatTargetAgents}/{supervisorScorecard.totalAgents} on target
                                                </div>
                                            </div>
                                        </div>
                                        <div className="bg-green-50 dark:bg-green-900/20 rounded p-2 border border-green-200 dark:border-green-700"
                                            title={`AHT Score: ${supervisorScorecard.ahtPercentage.toFixed(1)}% (${supervisorScorecard.ahtTargetAgents} out of ${supervisorScorecard.totalAgents} agents meeting ≤${convertDecimalMinutesToTime(config.aht.target)} target). Weight: 30% of total score.`}>
                                            <div className="text-center">
                                                <div className="text-lg font-bold text-green-600 dark:text-green-400">
                                                    {supervisorScorecard.ahtPercentage.toFixed(0)}%
                                                </div>
                                                <div className="text-xs text-green-600 dark:text-green-400">AHT (30%)</div>
                                                <div className="text-xs text-green-500 dark:text-green-400">
                                                    {supervisorScorecard.ahtTargetAgents}/{supervisorScorecard.totalAgents} on target
                                                </div>
                                            </div>
                                        </div>
                                        <div className="bg-purple-50 dark:bg-purple-900/20 rounded p-2 border border-purple-200 dark:border-purple-700"
                                            title={`QA Score: ${supervisorScorecard.qaPercentage.toFixed(1)}% (${supervisorScorecard.qaTargetAgents} out of ${supervisorScorecard.totalAgents} agents meeting ≥85% target). Weight: 20% of total score.`}>
                                            <div className="text-center">
                                                <div className="text-lg font-bold text-purple-600 dark:text-purple-400">
                                                    {supervisorScorecard.qaPercentage.toFixed(0)}%
                                                </div>
                                                <div className="text-xs text-purple-600 dark:text-purple-400">QA (20%)</div>
                                                <div className="text-xs text-purple-500 dark:text-purple-400">
                                                    {supervisorScorecard.qaTargetAgents}/{supervisorScorecard.totalAgents} on target
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Payout Tiers */}
                                <div className="lg:col-span-1">
                                    <div className="text-xs font-semibold text-gray-700 dark:text-gray-300 mb-2">Payout Tiers</div>
                                    <div className="space-y-1">
                                        {config.supervisorBonus.scorecardPayout.map((tier, index) => {
                                            const isCurrentTier = supervisorScorecard.scorecardScore >= tier.minScore && supervisorScorecard.scorecardScore <= tier.maxScore;
                                            return (
                                                <div key={index}
                                                    className={`text-xs p-1 rounded ${isCurrentTier
                                                        ? "bg-emerald-100 dark:bg-emerald-900/30 border border-emerald-300 dark:border-emerald-700 font-bold"
                                                        : "bg-gray-50 dark:bg-gray-800 text-gray-600 dark:text-gray-400"
                                                        }`}
                                                    title={`${tier.range}: ${formatCurrency(tier.amount)} bonus${isCurrentTier ? ' (Current Tier)' : ''}`}>
                                                    <div className="flex justify-between">
                                                        <span>{tier.range}</span>
                                                        <span>${(tier.amount / 1000).toFixed(0)}K</span>
                                                    </div>
                                                </div>
                                            );
                                        })}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                )}




            </div>
        </div>
    );
}; 