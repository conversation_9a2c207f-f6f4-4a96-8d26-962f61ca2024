"use client";
import React from 'react';

interface UploadStatusProps {
    uploadStatus: {
        mtd: boolean;
        aht: boolean;
        qa: boolean;
        dailyData: boolean;
    };
}

export function UploadStatus({ uploadStatus }: UploadStatusProps) {
    const hasAnyUpload = uploadStatus.mtd || uploadStatus.aht || uploadStatus.qa || uploadStatus.dailyData;

    if (!hasAnyUpload) {
        return null;
    }

    const statusItems = [
        {
            key: 'dailyData',
            label: 'Daily Data',
            isActive: uploadStatus.dailyData,
            description: null
        },
        {
            key: 'mtd',
            label: 'MTD Data',
            isActive: uploadStatus.mtd || uploadStatus.dailyData,
            description: uploadStatus.mtd ? 'MTD file data will override any calculated MTD values' : null
        },
        {
            key: 'aht',
            label: 'AHT Data',
            isActive: uploadStatus.aht || uploadStatus.dailyData,
            description: uploadStatus.aht ? 'AHT file data will override any existing AHT values' : null
        },
        {
            key: 'qa',
            label: 'QA Data',
            isActive: uploadStatus.qa,
            description: uploadStatus.qa ? 'QA file data will override any existing QA values' : null
        }
    ];

    const descriptions = statusItems
        .filter(item => item.description)
        .map(item => ({ key: item.key, description: item.description, color: getDescriptionColor(item.key) }));

    return (
        <div className="mb-6 p-4 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700">
            <h3 className="font-semibold text-gray-800 dark:text-gray-200 mb-3">Upload Status</h3>

            {/* Status Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-4 gap-3 text-sm">
                {statusItems.map(item => (
                    <StatusItem
                        key={item.key}
                        label={item.label}
                        isActive={item.isActive}
                    />
                ))}
            </div>

            {/* Descriptions */}
            {descriptions.length > 0 && (
                <div className="mt-2 space-y-1">
                    {descriptions.map(desc => (
                        <div key={desc.key} className={`text-xs ${desc.color}`}>
                            ℹ️ {desc.description}
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
}

interface StatusItemProps {
    label: string;
    isActive: boolean;
}

function StatusItem({ label, isActive }: StatusItemProps) {
    return (
        <div
            className={`flex items-center gap-2 p-2 rounded-lg ${isActive
                ? 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300'
                : 'bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400'
                }`}
        >
            <div
                className={`w-2 h-2 rounded-full ${isActive ? 'bg-green-500' : 'bg-gray-300'
                    }`}
            />
            {label} {isActive ? '✓' : '○'}
        </div>
    );
}

function getDescriptionColor(key: string): string {
    const colorMap = {
        mtd: 'text-amber-600 dark:text-amber-400',
        aht: 'text-blue-600 dark:text-blue-400',
        qa: 'text-purple-600 dark:text-purple-400'
    };
    return colorMap[key as keyof typeof colorMap] || 'text-gray-600 dark:text-gray-400';
} 