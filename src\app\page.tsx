"use client";
import { useState } from "react";
import { FileDrop } from "./FileDrop";
import { DailyDSATTable } from "./components/DailyDSATTable";
import { MTDRankingTable } from "./components/MTDRankingTable";
import { BonusStructure } from "./components/BonusStructure";
import { parseKPI, ParsedData } from "@/app/lib/parseKPI";
import { DepartmentProvider } from "./components/DepartmentContext";
import { DepartmentSelector } from "./components/DepartmentSelector";
import { ErrorBoundary } from "./components/ErrorBoundary";
import { LoadingOverlay } from "./components/LoadingSpinner";
import { UploadStatus } from "./components/UploadStatus";

// Enhanced state for dual-file support
interface DualFileState {
  dailyData: ParsedData;
  mtdData: ParsedData;
  ahtData: ParsedData;
  qaData: ParsedData;
  rawDailySurveyData: Array<{ date: string; agent: string; dsat: number | null; surveys: number }> | null; // Raw daily survey data
  teamPerformanceData: { [date: string]: number | null } | null; // Team Performance from Daily BI Data Total row
  mtdTeamPerformance: { dsat: number; aht: number; qa?: number } | null; // MTD Team Performance from MTD BI Data Total row
  uploadStatus: {
    mtd: boolean;
    aht: boolean;
    qa: boolean;
    dailyData: boolean; // Combines both APM Data and Daily BI Data
  };
}

function HomeContent() {
  const [dualFileState, setDualFileState] = useState<DualFileState>({
    dailyData: { dailyDSAT: [], mtdRanking: [], dates: [] },
    mtdData: { dailyDSAT: [], mtdRanking: [], dates: [] },
    ahtData: { dailyDSAT: [], mtdRanking: [], dates: [] },
    qaData: { dailyDSAT: [], mtdRanking: [], dates: [] },
    rawDailySurveyData: null,
    teamPerformanceData: null,
    mtdTeamPerformance: null,
    uploadStatus: { mtd: false, aht: false, qa: false, dailyData: false }
  });
  const [activeTable, setActiveTable] = useState<"daily" | "mtd" | "bonus">("daily");
  const [isProcessing, setIsProcessing] = useState(false);

  // Legacy text file handler
  const handleFileUpload = async (txt: string) => {
    setIsProcessing(true);
    try {
      const data = parseKPI(txt);
      setDualFileState({
        dailyData: data,
        mtdData: { dailyDSAT: [], mtdRanking: [], dates: [] },
        ahtData: { dailyDSAT: [], mtdRanking: [], dates: [] },
        qaData: { dailyDSAT: [], mtdRanking: [], dates: [] },
        rawDailySurveyData: null,
        teamPerformanceData: null,
        mtdTeamPerformance: null,
        uploadStatus: { mtd: false, aht: false, qa: false, dailyData: true }
      });
    } catch (error) {
      console.error('Error processing file:', error);
      throw error; // Re-throw to be caught by error boundary
    } finally {
      setIsProcessing(false);
    }
  };

  // Enhanced Excel file handler supporting dual files
  const handleEnhancedFileUpload = async (data: ParsedData & { rawDailySurveyData?: Array<{ date: string; agent: string; dsat: number | null; surveys: number }>; teamPerformanceData?: { [date: string]: number | null }; mtdTeamPerformance?: { dsat: number; aht: number; qa?: number } }, fileType: 'mtd' | 'aht' | 'qa' | 'apm-data' | 'bi-data' | 'mtd-bi-data') => {
    setIsProcessing(true);
    try {
      setDualFileState(prevState => {
        const newState = { ...prevState };

        if (fileType === 'mtd') {
          newState.mtdData = data;
          newState.mtdTeamPerformance = data.mtdTeamPerformance || null;
          newState.uploadStatus.mtd = true;
        } else if (fileType === 'aht') {
          newState.ahtData = data;
          newState.uploadStatus.aht = true;
        } else if (fileType === 'qa') {
          newState.qaData = data;
          newState.uploadStatus.qa = true;
        } else if (fileType === 'bi-data') {
          // Daily BI Data format contains daily DSAT data without survey counts
          newState.dailyData = data;
          newState.rawDailySurveyData = data.rawDailySurveyData || null;
          newState.teamPerformanceData = data.teamPerformanceData || null;
          newState.uploadStatus.dailyData = true;
        } else if (fileType === 'apm-data') {
          // APM Data format contains both daily and MTD data
          newState.dailyData = data;
          newState.rawDailySurveyData = data.rawDailySurveyData || null;
          newState.teamPerformanceData = null; // APM Data doesn't have team performance override
          newState.uploadStatus.dailyData = true;
        } else if (fileType === 'mtd-bi-data') {
          // MTD BI Data format contains only MTD ranking data (DSAT and AHT) plus team performance
          newState.mtdData = data;
          newState.mtdTeamPerformance = data.mtdTeamPerformance || null;
          newState.uploadStatus.mtd = true;
        }

        return newState;
      });
    } catch (error) {
      console.error('Error processing enhanced file upload:', error);
      throw error; // Re-throw to be caught by error boundary
    } finally {
      setIsProcessing(false);
    }
  };

  // Legacy handler for backward compatibility
  const handleNewFormatData = (data: ParsedData) => {
    handleEnhancedFileUpload(data, 'apm-data');
  };

  // Merge data intelligently for display
  const getMergedData = (): ParsedData => {
    const { dailyData, mtdData, ahtData, qaData, uploadStatus } = dualFileState;

    const merged: ParsedData = {
      dailyDSAT: dailyData.dailyDSAT,
      mtdRanking: dailyData.mtdRanking,
      dates: dailyData.dates
    };

    if (uploadStatus.mtd && mtdData.mtdRanking.length > 0) {
      const existingMTDMap = new Map(merged.mtdRanking.map(agent => [agent.name, agent]));

      mtdData.mtdRanking.forEach(mtdAgent => {
        const existing = existingMTDMap.get(mtdAgent.name);
        if (existing) {
          existing.dsat = mtdAgent.dsat;
          existing.aht = mtdAgent.aht || existing.aht;
        } else {
          merged.mtdRanking.push({
            ...mtdAgent,
            aht: mtdAgent.aht || 0,
            surveys: mtdAgent.surveys || 0,
            positiveSurveys: mtdAgent.positiveSurveys || 0,
            negativeSurveys: mtdAgent.negativeSurveys || 0
          });
        }
      });
    }

    if (uploadStatus.aht && ahtData.mtdRanking.length > 0) {
      const existingMTDMap = new Map(merged.mtdRanking.map(agent => [agent.name, agent]));

      ahtData.mtdRanking.forEach(ahtAgent => {
        const existing = existingMTDMap.get(ahtAgent.name);
        if (existing) {
          existing.aht = ahtAgent.aht || existing.aht;
        } else {
          merged.mtdRanking.push({
            ...ahtAgent,
            dsat: 50,
            qa: null,
            surveys: ahtAgent.surveys || 0,
            positiveSurveys: ahtAgent.positiveSurveys || 0,
            negativeSurveys: ahtAgent.negativeSurveys || 0
          });
        }
      });
    }

    if (uploadStatus.qa && qaData.mtdRanking.length > 0) {
      const existingMTDMap = new Map(merged.mtdRanking.map(agent => [agent.name, agent]));

      qaData.mtdRanking.forEach(qaAgent => {
        const existing = existingMTDMap.get(qaAgent.name);
        if (existing) {
          existing.qa = qaAgent.qa || existing.qa;
        } else {
          merged.mtdRanking.push({
            ...qaAgent,
            dsat: 50,
            aht: null,
            surveys: qaAgent.surveys || 0,
            positiveSurveys: qaAgent.positiveSurveys || 0,
            negativeSurveys: qaAgent.negativeSurveys || 0
          });
        }
      });
    }

    merged.mtdRanking.sort((a, b) => a.name.localeCompare(b.name));

    return merged;
  };

  const mergedData = getMergedData();
  const hasData = mergedData.dailyDSAT.length > 0 || mergedData.mtdRanking.length > 0;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900">
      {/* Header Section */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-purple-600/10 dark:from-blue-400/5 dark:to-purple-400/5"></div>
        <div className="relative max-w-7xl mx-auto px-6 py-12 sm:py-16">
          <div className="text-center">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
              KPI Dashboard
            </h1>
            <p className="text-lg sm:text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto leading-relaxed">
              Transform your satisfaction data into beautiful, shareable KPI reports with interactive tables and advanced poster generation.
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-6 pb-16">
        <div className="flex flex-col gap-12">
          {/* Upload Section */}
          <div className="w-full max-w-2xl mx-auto">
            <div className="mb-6">
              <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-2">
                Upload Your Data
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Upload Excel files (.xlsx, .xls) or text files (.txt, .csv, .md). The system automatically detects:
              </p>
              <div className="mt-3 grid grid-cols-1 sm:grid-cols-6 gap-3 text-sm">
                <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-3 border border-green-200 dark:border-green-700">
                  <div className="font-semibold text-green-700 dark:text-green-300">📝 APM Data</div>
                  <div className="text-green-600 dark:text-green-400">Full survey data with daily tracking</div>
                </div>
                <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3 border border-blue-200 dark:border-blue-700">
                  <div className="font-semibold text-blue-700 dark:text-blue-300">📊 Daily BI Data</div>
                  <div className="text-blue-600 dark:text-blue-400">Supervisor export format</div>
                </div>
                <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-3 border border-purple-200 dark:border-purple-700">
                  <div className="font-semibold text-purple-700 dark:text-purple-300">🏆 MTD DSAT</div>
                  <div className="text-purple-600 dark:text-purple-400">Month-to-date values</div>
                </div>
                <div className="bg-indigo-50 dark:bg-indigo-900/20 rounded-lg p-3 border border-indigo-200 dark:border-indigo-700">
                  <div className="font-semibold text-indigo-700 dark:text-indigo-300">📈 MTD BI Data</div>
                  <div className="text-indigo-600 dark:text-indigo-400">DSAT & AHT combined</div>
                </div>
                <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-3 border border-orange-200 dark:border-orange-700">
                  <div className="font-semibold text-orange-700 dark:text-orange-300">⏱️ AHT Data</div>
                  <div className="text-orange-600 dark:text-orange-400">Handle time metrics</div>
                </div>
                <div className="bg-pink-50 dark:bg-pink-900/20 rounded-lg p-3 border border-pink-200 dark:border-pink-700">
                  <div className="font-semibold text-pink-700 dark:text-pink-300">🎯 QA Data</div>
                  <div className="text-pink-600 dark:text-pink-400">Quality assurance scores</div>
                </div>
              </div>
            </div>

            {/* Upload Status */}
            <UploadStatus uploadStatus={dualFileState.uploadStatus} />

            {/* Department Selector */}
            <div className="mb-8">
              <DepartmentSelector />
            </div>

            <ErrorBoundary>
              <LoadingOverlay isLoading={isProcessing} text="Processing file...">
                <FileDrop
                  onFile={handleFileUpload}
                  onNewFormatData={handleNewFormatData}
                  onEnhancedFileUpload={handleEnhancedFileUpload}
                />
              </LoadingOverlay>
            </ErrorBoundary>
          </div>

          {/* Data Tables Section */}
          {hasData && (
            <div className="w-full">
              {/* Table Navigation */}
              <div className="flex justify-center mb-8">
                <div className="bg-white dark:bg-gray-800 rounded-xl p-1 shadow-md border border-gray-200 dark:border-gray-700">
                  <div className="flex gap-1">
                    <button
                      onClick={() => setActiveTable("daily")}
                      className={`px-6 py-3 rounded-lg font-semibold transition-all duration-200 ${activeTable === "daily"
                        ? "bg-blue-500 text-white shadow-sm"
                        : "text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                        }`}
                    >
                      📊 Daily Performance ({mergedData.dailyDSAT.length})
                    </button>
                    <button
                      onClick={() => setActiveTable("mtd")}
                      className={`px-6 py-3 rounded-lg font-semibold transition-all duration-200 ${activeTable === "mtd"
                        ? "bg-blue-500 text-white shadow-sm"
                        : "text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                        }`}
                    >
                      🏆 MTD Ranking ({mergedData.mtdRanking.length})
                    </button>
                    <button
                      onClick={() => setActiveTable("bonus")}
                      className={`px-6 py-3 rounded-lg font-semibold transition-all duration-200 ${activeTable === "bonus"
                        ? "bg-blue-500 text-white shadow-sm"
                        : "text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                        }`}
                    >
                      💰 Bonus Structure
                    </button>
                  </div>
                </div>
              </div>

              {/* Table Content */}
              <div className="w-full">
                <ErrorBoundary>
                  {activeTable === "daily" && mergedData.dailyDSAT.length > 0 && (
                    <DailyDSATTable
                      data={mergedData.dailyDSAT}
                      dates={mergedData.dates}
                      mtdData={mergedData.mtdRanking}
                      dailySurveyData={dualFileState.rawDailySurveyData || undefined}
                      teamPerformanceData={dualFileState.teamPerformanceData || undefined}
                    />
                  )}
                  {activeTable === "mtd" && mergedData.mtdRanking.length > 0 && (
                    <MTDRankingTable
                      data={mergedData.mtdRanking}
                      mtdTeamPerformance={dualFileState.mtdTeamPerformance}
                    />
                  )}
                  {activeTable === "bonus" && (
                    <BonusStructure
                      data={mergedData.mtdRanking}
                      mtdTeamPerformance={dualFileState.mtdTeamPerformance}
                    />
                  )}
                </ErrorBoundary>
                {((activeTable === "daily" && mergedData.dailyDSAT.length === 0) ||
                  (activeTable === "mtd" && mergedData.mtdRanking.length === 0)) && (
                    <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-12 text-center">
                      <div className="text-gray-400 text-6xl mb-4">📋</div>
                      <h3 className="text-xl font-semibold text-gray-600 dark:text-gray-300 mb-2">
                        No {activeTable === "daily" ? "Daily Performance" : "MTD Ranking"} Data
                      </h3>
                      <p className="text-gray-500 dark:text-gray-400">
                        Upload a file containing {activeTable === "daily" ? "daily performance records" : "MTD ranking data"} to see the table.
                      </p>
                    </div>
                  )}
              </div>
            </div>
          )}

          {/* No Data State */}
          {!hasData && (
            <div className="w-full text-center py-16">
              <div className="text-gray-400 text-8xl mb-6">📊</div>
              <h2 className="text-3xl font-bold text-gray-700 dark:text-gray-300 mb-4">
                Ready for Your Data
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-400 max-w-md mx-auto">
                Upload your file to see interactive tables with filtering, sorting, and advanced poster generation.
              </p>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}

export default function Home() {
  return (
    <DepartmentProvider>
      <HomeContent />
    </DepartmentProvider>
  );
}
