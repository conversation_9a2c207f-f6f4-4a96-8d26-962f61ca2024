"use client";
import React from 'react';
import { AlertTriangle, Zap, TrendingUp, Calendar } from 'lucide-react';

interface PerformanceStatusProps {
    totalDates: number;
    displayedDates: number;
    totalAgents: number;
    viewMode: string;
    onRecommendedModeChange?: (mode: string) => void;
}

export function PerformanceStatus({
    totalDates,
    displayedDates,
    totalAgents,
    viewMode,
    onRecommendedModeChange
}: PerformanceStatusProps) {
    const totalCells = displayedDates * totalAgents;
    const isLargeDataset = totalCells > 1000;
    const isVeryLarge = totalCells > 2000;

    // Performance recommendations
    const getRecommendation = () => {
        if (totalDates <= 7) return null;
        if (totalDates <= 14) return { mode: 'recent', label: 'Recent 7', reason: 'Good performance' };
        if (totalDates <= 31) return { mode: 'currentMonth', label: 'This Month', reason: 'Monthly view optimal' };
        return { mode: 'currentMonth', label: 'This Month', reason: 'Best for 31+ days' };
    };

    const recommendation = getRecommendation();
    const shouldShowRecommendation = recommendation && viewMode === 'all' && displayedDates > 20;

    if (!isLargeDataset && !shouldShowRecommendation) {
        return null;
    }

    return (
        <div className={`p-3 rounded-lg border ${isVeryLarge
            ? 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
            : isLargeDataset
                ? 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800'
                : 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800'
            }`}>
            <div className="flex items-start gap-3">
                <div className="flex-shrink-0 mt-0.5">
                    {isVeryLarge ? (
                        <AlertTriangle className="w-4 h-4 text-red-600 dark:text-red-400" />
                    ) : isLargeDataset ? (
                        <Zap className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />
                    ) : (
                        <TrendingUp className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                    )}
                </div>

                <div className="flex-1 min-w-0">
                    <div className={`text-sm font-medium ${isVeryLarge
                        ? 'text-red-800 dark:text-red-300'
                        : isLargeDataset
                            ? 'text-yellow-800 dark:text-yellow-300'
                            : 'text-blue-800 dark:text-blue-300'
                        }`}>
                        {isVeryLarge
                            ? 'Large Dataset Performance Impact'
                            : isLargeDataset
                                ? 'Performance Notice'
                                : 'Dataset Info'
                        }
                    </div>

                    <div className={`text-xs mt-1 ${isVeryLarge
                        ? 'text-red-600 dark:text-red-400'
                        : isLargeDataset
                            ? 'text-yellow-600 dark:text-yellow-400'
                            : 'text-blue-600 dark:text-blue-400'
                        }`}>
                        Displaying {displayedDates.toLocaleString()} days × {totalAgents} agents = {totalCells.toLocaleString()} data points
                        {displayedDates < totalDates && ` (${totalDates - displayedDates} days filtered)`}
                    </div>

                    {shouldShowRecommendation && recommendation && onRecommendedModeChange && (
                        <div className="mt-2 flex items-center gap-2">
                            <span className="text-xs text-gray-600 dark:text-gray-400">
                                Recommended:
                            </span>
                            <button
                                onClick={() => onRecommendedModeChange(recommendation.mode)}
                                className="inline-flex items-center gap-1 px-2 py-1 text-xs bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                            >
                                <Calendar className="w-3 h-3" />
                                {recommendation.label}
                            </button>
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                                ({recommendation.reason})
                            </span>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
} 