"use client";
import React, { useRef, useState } from "react";
import { DailyDSAT, MTDRanking } from "@/app/lib/parseKPI";
import { useDepartment } from './DepartmentContext';
import clsx from "clsx";
import { toPng } from "html-to-image";

// Interface for daily survey data
interface DailySurveyData {
    date: string;
    agent: string;
    dsat: number | null;
    surveys: number;
}

interface DailyDSATPosterProps {
    data: DailyDSAT[];
    dates: string[];
    filteredData?: DailyDSAT[];
    searchTerm?: string;
    mtdData?: MTDRanking[];
    dailySurveyData?: DailySurveyData[];
    teamPerformanceData?: {
        dailyDSAT: { [date: string]: number };
        average: number;
        isWeighted?: boolean;
        isBIData?: boolean;
    };
    performanceStats?: {
        excellent: number;
        good: number;
        warning: number;
        needsImprovement: number;
        avgScore: number;
        total: number;
    };
    displayDates?: string[];
    sortedAndFilteredData?: DailyDSAT[];
    // NEW: Function to get agent average - passed from table
    getAgentAverage?: (agent: DailyDSAT, datesToUse: string[]) => number | null;
}

/**
 * Get performance color class based on DSAT score and thresholds
 */
function getPerformanceColor(score: number | null, excellent?: number, good?: number, target?: number): string {
    if (score === null) return "text-gray-400 dark:text-gray-500";

    const excellentThreshold = excellent || 37.4;
    const targetThreshold = target || 44;

    if (score <= excellentThreshold) return "text-emerald-600 dark:text-emerald-400";
    if (score <= targetThreshold) return "text-amber-600 dark:text-amber-400";
    return "text-red-500 dark:text-red-400";
}



/**
 * DISPLAY-ONLY Daily DSAT Poster Component
 * Shows exactly what's displayed in the table without any calculations
 */
export const DailyDSATPoster: React.FC<DailyDSATPosterProps> = ({
    data,
    dates,
    filteredData,
    searchTerm,
    dailySurveyData,
    teamPerformanceData,
    displayDates,
    sortedAndFilteredData,
    performanceStats,
    getAgentAverage
}) => {
    const ref = useRef<HTMLDivElement>(null);
    const { config } = useDepartment();
    const [isDownloading, setIsDownloading] = useState(false);

    // Use the exact same data and dates from the table - NO CALCULATIONS
    const displayData = sortedAndFilteredData || filteredData || data;
    const posterDates = displayDates || dates;
    const todayDate = new Date().toLocaleDateString();

    // Use pre-calculated team performance data directly from table
    const teamDSAT = teamPerformanceData?.average || null;

    // Get daily team DSAT directly from pre-calculated data
    const getDailyTeamDSAT = (date: string): number | null => {
        return teamPerformanceData?.dailyDSAT[date] || null;
    };

    // Download handler
    const handleDownload = async () => {
        if (!ref.current || isDownloading) return;

        setIsDownloading(true);
        try {
            console.log('Starting poster download...');
            console.log('Using html-to-image (toPng) for better compatibility...');

            const dataUrl = await toPng(ref.current, {
                pixelRatio: 2,
                backgroundColor: '#0f172a',
                style: {
                    transform: 'scale(1)',
                    transformOrigin: 'top left'
                },
                filter: (node) => {
                    // Skip elements that might cause issues
                    if (node.classList?.contains('ignore-screenshot')) {
                        return false;
                    }
                    return true;
                }
            });

            console.log('Canvas rendered, creating download link...');
            // Create download link
            const link = document.createElement('a');
            link.download = `daily-dsat-report-${todayDate}.png`;
            link.href = dataUrl;

            // Add to DOM temporarily to ensure it works across browsers
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            console.log('Download triggered successfully');
        } catch (error) {
            console.error('Error downloading poster:', error);
            // Provide user feedback with more specific error information
            const errorMessage = error instanceof Error ? error.message : String(error);
            if (errorMessage.includes('oklch') || errorMessage.includes('color')) {
                alert('Download failed due to color compatibility issues. This is a known issue with certain CSS color functions. Please try using a different browser or contact support.');
            } else {
                alert('Failed to download poster. Please try again or check the console for more details.');
            }
        } finally {
            setIsDownloading(false);
        }
    };

    // Use pre-calculated performance stats directly from table
    const stats = performanceStats || {
        excellent: 0,
        good: 0,
        warning: 0,
        needsImprovement: 0,
        total: displayData.length,
        avgScore: teamDSAT || 0
    };



    return (
        <div className="relative group">
            <div
                ref={ref}
                className="relative w-[1200px] min-h-[1600px] bg-slate-900 rounded-3xl shadow-2xl overflow-hidden"
            >
                <div className="relative h-full flex flex-col p-6">
                    {/* Header */}
                    <div className="relative mb-6">
                        <div className="bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700 rounded-2xl p-6 text-center shadow-lg">
                            <h1 className="text-4xl font-black text-white mb-2 tracking-tight">
                                Daily DSAT Report
                            </h1>
                            <p className="text-blue-100 font-medium">
                                Customer Support • Performance Overview • {todayDate}
                                {posterDates.length > 0 && posterDates.length !== dates.length && (
                                    <span className="block text-sm mt-1 text-blue-200">
                                        {posterDates[0]} - {posterDates[posterDates.length - 1]}
                                    </span>
                                )}
                                {searchTerm && (
                                    <span className="block text-sm mt-1 text-blue-200">
                                        Filtered: &ldquo;{searchTerm}&rdquo;
                                    </span>
                                )}
                            </p>
                        </div>
                    </div>

                    {/* Performance Summary Cards */}
                    <div className="grid grid-cols-5 gap-3 mb-6">
                        <div className="bg-slate-800 rounded-lg p-4 text-center border border-slate-700">
                            <div className="text-3xl font-bold text-white">{stats.total}</div>
                            <div className="text-xs text-slate-400 uppercase tracking-wide">TOTAL</div>
                        </div>
                        <div className="bg-emerald-900/50 rounded-lg p-4 text-center border border-emerald-600">
                            <div className="text-2xl font-bold text-emerald-400">{stats.excellent}</div>
                            <div className="text-xs text-emerald-400 uppercase tracking-wide">TIER 1</div>
                            <div className="text-xs text-emerald-500">≤{config.dsat.excellent}%</div>
                        </div>
                        <div className="bg-amber-900/50 rounded-lg p-4 text-center border border-amber-600">
                            <div className="text-2xl font-bold text-amber-400">{stats.good}</div>
                            <div className="text-xs text-amber-400 uppercase tracking-wide">TIER 2</div>
                            <div className="text-xs text-amber-500">{config.dsat.excellent + 0.1}-{config.dsat.good}%</div>
                        </div>
                        <div className="bg-orange-900/50 rounded-lg p-4 text-center border border-orange-600">
                            <div className="text-2xl font-bold text-orange-400">{stats.warning}</div>
                            <div className="text-xs text-orange-400 uppercase tracking-wide">TIER 3</div>
                            <div className="text-xs text-orange-500">{config.dsat.good + 0.1}-{config.dsat.target}%</div>
                        </div>
                        <div className="bg-red-900/50 rounded-lg p-4 text-center border border-red-600">
                            <div className="text-2xl font-bold text-red-400">{stats.needsImprovement}</div>
                            <div className="text-xs text-red-400 uppercase tracking-wide">ABOVE T3</div>
                            <div className="text-xs text-red-500">&gt;{config.dsat.target}%</div>
                        </div>
                    </div>

                    {/* Performance Tracking Section */}
                    <div className="mb-6">
                        <div className="text-center mb-4">
                            <h2 className="text-white text-xl font-bold flex items-center justify-center gap-2">
                                <span className="text-lg">📊</span> Performance Tracking
                            </h2>
                            <div className="text-slate-400 text-sm">
                                {posterDates.length > 0 && `${posterDates[0]} - ${posterDates[posterDates.length - 1]}`}
                            </div>
                        </div>
                    </div>

                    {/* Daily Performance Table */}
                    <div className="bg-slate-800 rounded-lg border border-slate-700 overflow-hidden flex-1">
                        <div className="px-4 py-2 border-b border-slate-600">
                            <div className="flex items-center justify-between">
                                <h2 className="text-white font-bold text-left">
                                    Daily DSAT Tracking
                                </h2>
                                {posterDates.length > 7 && (
                                    <div className="text-xs text-slate-400">
                                        📊 {posterDates.length} days • Scroll horizontally →
                                    </div>
                                )}
                            </div>
                            <div className="text-sm text-slate-400">
                                Tiers: ≤{config.dsat.excellent}% (Excellent) • {config.dsat.excellent + 0.1}-{config.dsat.good}% (Good) • &gt;{config.dsat.good}% (Focus)
                            </div>
                        </div>
                        <div className="overflow-x-auto relative">
                            <table className={`text-xs ${posterDates.length <= 7 ? 'min-w-full' : 'w-max'}`}>
                                <thead className="bg-slate-700 sticky top-0 z-20">
                                    <tr>
                                        <th className="px-2 py-2 text-left text-xs font-bold text-white sticky left-0 bg-slate-700 z-30 min-w-[120px] border-r border-slate-600">Agent</th>
                                        {posterDates.length > 0 && (
                                            <th className="px-2 py-2 text-center text-xs font-bold text-white sticky left-[120px] bg-slate-700 z-30 min-w-[60px] border-r border-slate-600">Avg</th>
                                        )}
                                        {posterDates.map(date => {
                                            // Parse date manually to avoid timezone issues
                                            const [year, month, day] = date.split('-').map(Number);
                                            const dateObj = new Date(year, month - 1, day); // month is 0-indexed

                                            return (
                                                <th key={date} className={`px-2 py-2 text-center text-xs font-bold text-white ${posterDates.length <= 7 ? 'min-w-[80px]' : 'min-w-[100px]'}`}>
                                                    <div>
                                                        <div>{dateObj.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}</div>
                                                        <div className="text-xs text-slate-400 font-normal">
                                                            {dateObj.toLocaleDateString('en-US', { weekday: 'short' })}
                                                        </div>
                                                    </div>
                                                </th>
                                            );
                                        })}
                                    </tr>
                                </thead>
                                <tbody>
                                    {/* Team Performance Row */}
                                    <tr className="bg-blue-900/50 border-b border-blue-600">
                                        <td className="px-2 py-2 font-bold text-blue-300 text-xs sticky left-0 bg-blue-900/50 z-20 min-w-[120px] border-r border-slate-600">🏆 Team Performance</td>
                                        {posterDates.length > 0 && (
                                            <td className="px-2 py-2 text-center text-xs sticky left-[120px] bg-blue-900/50 z-20 min-w-[60px] border-r border-slate-600">
                                                {teamDSAT !== null ? (
                                                    <div>
                                                        <div className={clsx("font-bold", getPerformanceColor(teamDSAT, config.dsat.excellent, config.dsat.good, config.dsat.target))}>
                                                            {teamDSAT.toFixed(2)}%
                                                        </div>
                                                        {dailySurveyData && dailySurveyData.length > 0 && (() => {
                                                            const displayedAgentNames = displayData.map(agent => agent.name);
                                                            const totalSurveys = dailySurveyData.filter(record =>
                                                                posterDates.includes(record.date) && displayedAgentNames.includes(record.agent)
                                                            ).reduce((sum, record) => sum + record.surveys, 0);
                                                            const positiveSurveys = dailySurveyData.filter(record =>
                                                                posterDates.includes(record.date) && displayedAgentNames.includes(record.agent)
                                                            ).reduce((sum, record) => {
                                                                if (record.dsat !== null && record.surveys > 0) {
                                                                    return sum + Math.round(record.surveys * (1 - record.dsat / 100));
                                                                }
                                                                return sum;
                                                            }, 0);
                                                            const negativeSurveys = totalSurveys - positiveSurveys;
                                                            return totalSurveys > 0 ? (
                                                                <div className="flex items-center justify-center gap-1 mt-1">
                                                                    <div className="min-w-[22px] h-5 bg-emerald-900 border border-emerald-600 rounded-full flex items-center justify-center text-emerald-300 text-xs font-bold px-1">
                                                                        {positiveSurveys}
                                                                    </div>
                                                                    <div className="min-w-[22px] h-5 bg-red-900 border border-red-600 rounded-full flex items-center justify-center text-red-300 text-xs font-bold px-1">
                                                                        {negativeSurveys}
                                                                    </div>
                                                                </div>
                                                            ) : null;
                                                        })()}
                                                    </div>
                                                ) : (
                                                    <div className="text-slate-400 font-bold">-</div>
                                                )}
                                            </td>
                                        )}
                                        {posterDates.map(date => {
                                            const dailyTeamScore = getDailyTeamDSAT(date);
                                            return (
                                                <td key={date} className={`px-2 py-2 text-center text-xs ${posterDates.length <= 7 ? 'min-w-[80px]' : 'min-w-[100px]'}`}>
                                                    {dailyTeamScore !== null ? (
                                                        <div>
                                                            <div className={clsx("font-bold", getPerformanceColor(dailyTeamScore, config.dsat.excellent, config.dsat.good, config.dsat.target))}>
                                                                {dailyTeamScore.toFixed(2)}%
                                                            </div>
                                                            {dailySurveyData && dailySurveyData.length > 0 && (() => {
                                                                const displayedAgentNames = displayData.map(agent => agent.name);
                                                                const dailyTotalSurveys = dailySurveyData.filter(record =>
                                                                    record.date === date && displayedAgentNames.includes(record.agent)
                                                                ).reduce((sum, record) => sum + record.surveys, 0);
                                                                const dailyPositiveSurveys = dailySurveyData.filter(record =>
                                                                    record.date === date && displayedAgentNames.includes(record.agent)
                                                                ).reduce((sum, record) => {
                                                                    if (record.dsat !== null && record.surveys > 0) {
                                                                        return sum + Math.round(record.surveys * (1 - record.dsat / 100));
                                                                    }
                                                                    return sum;
                                                                }, 0);
                                                                const dailyNegativeSurveys = dailyTotalSurveys - dailyPositiveSurveys;
                                                                return dailyTotalSurveys > 0 ? (
                                                                    <div className="flex items-center justify-center gap-1 mt-1">
                                                                        <div className="min-w-[20px] h-4 bg-emerald-900 border border-emerald-600 rounded-full flex items-center justify-center text-emerald-300 text-xs font-bold px-1">
                                                                            {dailyPositiveSurveys}
                                                                        </div>
                                                                        <div className="min-w-[20px] h-4 bg-red-900 border border-red-600 rounded-full flex items-center justify-center text-red-300 text-xs font-bold px-1">
                                                                            {dailyNegativeSurveys}
                                                                        </div>
                                                                    </div>
                                                                ) : null;
                                                            })()}
                                                        </div>
                                                    ) : (
                                                        <div className="text-slate-400 font-bold">-</div>
                                                    )}
                                                </td>
                                            );
                                        })}
                                    </tr>

                                    {/* Individual Agent Rows - Show all agents */}
                                    {displayData.map((agent, index) => {
                                        const agentAvg = getAgentAverage ? getAgentAverage(agent, posterDates) : null;
                                        return (
                                            <tr key={agent.name} className={index % 2 === 0 ? "bg-slate-800" : "bg-slate-700"}>
                                                <td className="px-2 py-1 font-medium text-slate-300 text-xs truncate sticky left-0 z-15 min-w-[120px] border-r border-slate-600"
                                                    style={{ backgroundColor: index % 2 === 0 ? 'rgb(30 41 59)' : 'rgb(51 65 85)' }}
                                                    title={agent.name}>
                                                    {agent.name}
                                                </td>
                                                {posterDates.length > 0 && (
                                                    <td className="px-2 py-1 text-center text-xs sticky left-[120px] z-15 min-w-[60px] border-r border-slate-600"
                                                        style={{ backgroundColor: index % 2 === 0 ? 'rgb(30 41 59)' : 'rgb(51 65 85)' }}>
                                                        {agentAvg !== null ? (
                                                            <div>
                                                                <div className={clsx("font-bold", getPerformanceColor(agentAvg, config.dsat.excellent, config.dsat.good, config.dsat.target))}>
                                                                    {agentAvg.toFixed(2)}%
                                                                </div>
                                                                {dailySurveyData && dailySurveyData.length > 0 && (() => {
                                                                    const totalSurveys = dailySurveyData.filter(record =>
                                                                        posterDates.includes(record.date) && record.agent === agent.name
                                                                    ).reduce((sum, record) => sum + record.surveys, 0);
                                                                    const positiveSurveys = dailySurveyData.filter(record =>
                                                                        posterDates.includes(record.date) && record.agent === agent.name
                                                                    ).reduce((sum, record) => {
                                                                        if (record.dsat !== null && record.surveys > 0) {
                                                                            return sum + Math.round(record.surveys * (1 - record.dsat / 100));
                                                                        }
                                                                        return sum;
                                                                    }, 0);
                                                                    const negativeSurveys = totalSurveys - positiveSurveys;
                                                                    return totalSurveys > 0 ? (
                                                                        <div className="flex items-center justify-center gap-1 mt-1">
                                                                            <div className="min-w-[20px] h-4 bg-emerald-900 border border-emerald-600 rounded-full flex items-center justify-center text-emerald-300 text-xs font-semibold px-1">
                                                                                {positiveSurveys}
                                                                            </div>
                                                                            <div className="min-w-[20px] h-4 bg-red-900 border border-red-600 rounded-full flex items-center justify-center text-red-300 text-xs font-semibold px-1">
                                                                                {negativeSurveys}
                                                                            </div>
                                                                        </div>
                                                                    ) : null;
                                                                })()}
                                                            </div>
                                                        ) : (
                                                            <div className="text-slate-400 font-medium">-</div>
                                                        )}
                                                    </td>
                                                )}
                                                {posterDates.map(date => {
                                                    const score = agent.dates[date];
                                                    const surveyRecord = dailySurveyData?.find(record =>
                                                        record.date === date && record.agent === agent.name
                                                    );
                                                    return (
                                                        <td key={date} className={`px-2 py-1 text-center text-xs ${posterDates.length <= 7 ? 'min-w-[80px]' : 'min-w-[100px]'}`}>
                                                            {score !== null ? (
                                                                <div>
                                                                    <div className={clsx("font-bold", getPerformanceColor(score, config.dsat.excellent, config.dsat.good, config.dsat.target))}>
                                                                        {score.toFixed(2)}%
                                                                    </div>
                                                                    {dailySurveyData && dailySurveyData.length > 0 && surveyRecord && surveyRecord.surveys > 0 && (
                                                                        <div className="flex items-center justify-center gap-1 mt-1">
                                                                            <div className="min-w-[20px] h-4 bg-emerald-900 border border-emerald-600 rounded-full flex items-center justify-center text-emerald-300 text-xs font-semibold px-1">
                                                                                {surveyRecord.dsat !== null && surveyRecord.surveys > 0
                                                                                    ? Math.round(surveyRecord.surveys * (1 - surveyRecord.dsat / 100))
                                                                                    : 0}
                                                                            </div>
                                                                            <div className="min-w-[20px] h-4 bg-red-900 border border-red-600 rounded-full flex items-center justify-center text-red-300 text-xs font-semibold px-1">
                                                                                {surveyRecord.dsat !== null && surveyRecord.surveys > 0
                                                                                    ? Math.round(surveyRecord.surveys * (surveyRecord.dsat / 100))
                                                                                    : 0}
                                                                            </div>
                                                                        </div>
                                                                    )}
                                                                </div>
                                                            ) : (
                                                                <div className="text-slate-400 font-medium">-</div>
                                                            )}
                                                        </td>
                                                    );
                                                })}
                                            </tr>
                                        );
                                    })}

                                </tbody>
                            </table>
                        </div>
                    </div>

                    {/* Footer */}
                    <div className="mt-4 text-center text-sm text-slate-400">
                        Tiers: ≤{config.dsat.excellent}% (Green) • {config.dsat.excellent + 0.1}-{config.dsat.good}% (Yellow) • &gt;{config.dsat.good}% (Red) • Generated: {todayDate} • Agents: {displayData.length} • Team Average: {teamDSAT?.toFixed(2)}%
                    </div>

                    {/* Download Button */}
                    <div className="mt-6 text-center">
                        <button
                            onClick={handleDownload}
                            disabled={isDownloading}
                            className={`font-bold py-3 px-6 rounded-lg transition-colors ${isDownloading
                                ? 'bg-gray-500 cursor-not-allowed'
                                : 'bg-blue-600 hover:bg-blue-700'
                                } text-white`}
                        >
                            {isDownloading ? (
                                <>
                                    <span className="animate-spin inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></span>
                                    Generating...
                                </>
                            ) : (
                                <>📥 Download Daily DSAT Report</>
                            )}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
}; 