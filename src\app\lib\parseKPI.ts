export interface DailyDSAT {
    name: string;
    dates: { [date: string]: number | null };
}

export interface MTDRanking {
    name: string;
    dsat: number;
    aht: number | null; // Allow null for missing AHT data
    qa: number | null; // QA percentage (0-100)
    surveys?: number; // Optional: total surveys for the period
    positiveSurveys?: number; // Optional: positive surveys
    negativeSurveys?: number; // Optional: negative surveys
}

export interface ParsedData {
    dailyDSAT: DailyDSAT[];
    mtdRanking: MTDRanking[];
    dates: string[];
}

// Helper function to detect file format
function detectFormat(raw: string): 'mixed' | 'three-column' | 'tabular' {
    const lines = raw.split(/\r?\n/).map(line => line.trim()).filter(line => line.length > 0);

    if (lines.length === 0) return 'tabular';

    // Check for mixed format (has both daily and MTD sections)
    const hasDailySection = lines.some(line =>
        line.toLowerCase().includes('daily') && line.toLowerCase().includes('dsat')
    );
    const hasMTDSection = lines.some(line =>
        line.toLowerCase().includes('mtd') && line.toLowerCase().includes('dsat')
    );

    if (hasDailySection && hasMTDSection) {
        return 'mixed';
    }

    // Check if it looks like pure 3-column format (Date, Agent, DSAT)
    const firstDataLine = lines.find(line =>
        !line.toLowerCase().includes('date') &&
        !line.toLowerCase().includes('agent') &&
        !line.toLowerCase().includes('dsat') &&
        !line.toLowerCase().includes('daily') &&
        !line.toLowerCase().includes('mtd') &&
        line.length > 0
    );

    if (firstDataLine) {
        // Try different separators
        const commaParts = firstDataLine.split(',');
        const tabParts = firstDataLine.split('\t');
        const spaceParts = firstDataLine.split(/\s+/);

        // Check if any separator gives us exactly 3 parts with a date-like pattern
        const datePattern = /\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}/;

        if ((commaParts.length === 3 && datePattern.test(commaParts[0].trim())) ||
            (tabParts.length === 3 && datePattern.test(tabParts[0].trim())) ||
            (spaceParts.length === 3 && datePattern.test(spaceParts[0].trim()))) {
            return 'three-column';
        }
    }

    return 'tabular';
}

// Helper function to parse 3-column format
function parseThreeColumnFormat(raw: string): ParsedData {
    const lines = raw.split(/\r?\n/).map(line => line.trim()).filter(line => line.length > 0);

    const result: ParsedData = {
        dailyDSAT: [],
        mtdRanking: [],
        dates: []
    };

    const agentData: { [agentName: string]: { [date: string]: number } } = {};
    const allDates = new Set<string>();

    for (const line of lines) {
        // Skip header lines
        if (line.toLowerCase().includes('date') &&
            (line.toLowerCase().includes('agent') || line.toLowerCase().includes('name')) &&
            line.toLowerCase().includes('dsat')) {
            continue;
        }

        // Try different separators
        let parts: string[] = [];
        if (line.includes(',')) {
            parts = line.split(',');
        } else if (line.includes('\t')) {
            parts = line.split('\t');
        } else {
            parts = line.split(/\s+/);
        }

        if (parts.length >= 3) {
            const dateStr = parts[0].trim();
            const agentName = parts[1].trim();
            const dsatStr = parts[2].trim();

            // Validate date format
            const datePattern = /\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}/;
            if (!datePattern.test(dateStr)) continue;

            // Parse DSAT value
            const dsatValue = parseFloat(dsatStr.replace('%', ''));
            if (isNaN(dsatValue) || dsatValue < 0 || dsatValue > 100) continue;

            // Normalize date format (convert to MM/DD/YYYY)
            let normalizedDate = dateStr;
            if (dateStr.includes('-')) {
                const dateParts = dateStr.split('-');
                if (dateParts.length === 3) {
                    // Assume MM-DD-YYYY or DD-MM-YYYY, convert to MM/DD/YYYY
                    normalizedDate = dateParts.join('/');
                }
            }

            // Store data
            if (!agentData[agentName]) {
                agentData[agentName] = {};
            }
            agentData[agentName][normalizedDate] = dsatValue;
            allDates.add(normalizedDate);
        }
    }

    // Convert to required format
    result.dates = Array.from(allDates).sort((a, b) => {
        const dateA = new Date(a);
        const dateB = new Date(b);

        // Handle invalid dates
        if (isNaN(dateA.getTime()) && isNaN(dateB.getTime())) return 0;
        if (isNaN(dateA.getTime())) return 1;
        if (isNaN(dateB.getTime())) return -1;

        return dateA.getTime() - dateB.getTime();
    });

    for (const [agentName, dateValues] of Object.entries(agentData)) {
        const dailyEntry: DailyDSAT = {
            name: agentName,
            dates: {}
        };

        // Initialize all dates as null
        result.dates.forEach(date => {
            dailyEntry.dates[date] = dateValues[date] || null;
        });

        result.dailyDSAT.push(dailyEntry);
    }

    return result;
}

// Helper function to parse mixed format (both daily 3-column and MTD tabular)
function parseMixedFormat(raw: string): ParsedData {
    const lines = raw.split(/\r?\n/).map(line => line.trim()).filter(line => line.length > 0);

    const result: ParsedData = {
        dailyDSAT: [],
        mtdRanking: [],
        dates: []
    };

    let currentSection = '';
    const agentData: { [agentName: string]: { [date: string]: number } } = {};
    const allDates = new Set<string>();

    for (const line of lines) {
        // Check for Daily DSAT section
        if (line.toLowerCase().includes('daily') && line.toLowerCase().includes('dsat')) {
            currentSection = 'daily';
            continue;
        }

        // Check for MTD section
        if (line.toLowerCase().includes('mtd') && line.toLowerCase().includes('dsat')) {
            currentSection = 'mtd';
            continue;
        }

        // Parse Daily DSAT data (3-column format)
        if (currentSection === 'daily') {
            // Skip header lines
            if (line.toLowerCase().includes('date') &&
                (line.toLowerCase().includes('agent') || line.toLowerCase().includes('name')) &&
                line.toLowerCase().includes('dsat')) {
                continue;
            }

            // Skip empty lines
            if (!line || line.trim().length === 0) continue;

            // Try different separators
            let parts: string[] = [];
            if (line.includes(',')) {
                parts = line.split(',');
            } else if (line.includes('\t')) {
                parts = line.split('\t');
            } else {
                parts = line.split(/\s+/);
            }

            if (parts.length >= 3) {
                const dateStr = parts[0].trim();
                const agentName = parts[1].trim();
                const dsatStr = parts[2].trim();

                // Validate date format
                const datePattern = /\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}/;
                if (!datePattern.test(dateStr)) continue;

                // Parse DSAT value
                const dsatValue = parseFloat(dsatStr.replace('%', ''));
                if (isNaN(dsatValue) || dsatValue < 0 || dsatValue > 100) continue;

                // Normalize date format
                let normalizedDate = dateStr;
                if (dateStr.includes('-')) {
                    const dateParts = dateStr.split('-');
                    if (dateParts.length === 3) {
                        normalizedDate = dateParts.join('/');
                    }
                }

                // Store data
                if (!agentData[agentName]) {
                    agentData[agentName] = {};
                }
                agentData[agentName][normalizedDate] = dsatValue;
                allDates.add(normalizedDate);
            }
        }
        // Parse MTD data (tabular format)
        else if (currentSection === 'mtd') {
            // Skip header lines and empty lines
            if (!line ||
                line.toLowerCase().includes('name') ||
                line.toLowerCase().includes('dsat') ||
                line.toLowerCase().includes('aht') ||
                line.trim().length === 0) {
                continue;
            }

            // Split by tabs for MTD data
            const parts = line.split(/\t+/);
            if (parts.length >= 3) {
                const name = parts[0].trim();
                const dsatStr = parts[1].trim().replace('%', '');
                const ahtStr = parts[2].trim();

                const dsat = parseFloat(dsatStr);
                const aht = parseFloat(ahtStr);

                if (name && !isNaN(dsat) && !isNaN(aht) && dsat >= 0 && dsat <= 100 && aht > 0) {
                    result.mtdRanking.push({
                        name,
                        dsat,
                        aht,
                        qa: null
                    });
                }
            }
        }
    }

    // Convert daily data to required format
    result.dates = Array.from(allDates).sort((a, b) => {
        const dateA = new Date(a);
        const dateB = new Date(b);

        // Handle invalid dates
        if (isNaN(dateA.getTime()) && isNaN(dateB.getTime())) return 0;
        if (isNaN(dateA.getTime())) return 1;
        if (isNaN(dateB.getTime())) return -1;

        return dateA.getTime() - dateB.getTime();
    });

    for (const [agentName, dateValues] of Object.entries(agentData)) {
        const dailyEntry: DailyDSAT = {
            name: agentName,
            dates: {}
        };

        // Initialize all dates as null
        result.dates.forEach(date => {
            dailyEntry.dates[date] = dateValues[date] || null;
        });

        result.dailyDSAT.push(dailyEntry);
    }

    return result;
}

// Original tabular format parser
function parseTabularFormat(raw: string): ParsedData {
    const lines = raw.split(/\r?\n/).map(line => line.trim()).filter(line => line.length > 0);

    const result: ParsedData = {
        dailyDSAT: [],
        mtdRanking: [],
        dates: []
    };

    let currentSection = '';
    let dateHeaders: string[] = [];

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];

        // Check for Daily DSAT section
        if (line.toLowerCase().includes('daily dsat')) {
            currentSection = 'daily';

            // Split by tabs to get the header structure
            const parts = line.split(/\t+/);

            // Extract dates from the header (skip first column which is "Daily DSAT")
            if (parts.length > 1) {
                dateHeaders = parts.slice(1).filter(part => {
                    // Check if it looks like a date
                    return part.match(/\d{1,2}\/\d{1,2}\/\d{4}/);
                });
                result.dates = dateHeaders;
            }
            continue;
        }

        // Check for MTD section
        if (line.toLowerCase().includes('mtd') && (line.toLowerCase().includes('dsat') || line.toLowerCase().includes('aht'))) {
            currentSection = 'mtd';
            continue;
        }

        // Parse Daily DSAT data lines
        if (currentSection === 'daily' && dateHeaders.length > 0) {
            // Skip empty lines or lines that don't contain agent data
            if (!line || line.toLowerCase().includes('dsat') || line.toLowerCase().includes('date')) {
                continue;
            }

            // Split by tabs to get aligned data
            const parts = line.split(/\t/);

            if (parts.length > 0) {
                const name = parts[0].trim().replace(/,$/, '');

                if (name && name.length > 0) {
                    const dailyEntry: DailyDSAT = {
                        name,
                        dates: {}
                    };

                    // Initialize all dates as null
                    dateHeaders.forEach(date => {
                        dailyEntry.dates[date] = null;
                    });

                    // Map data columns to dates (skip first column which is the name)
                    for (let j = 1; j < parts.length && j - 1 < dateHeaders.length; j++) {
                        const cellData = parts[j].trim();
                        const dateKey = dateHeaders[j - 1];

                        if (cellData && cellData.includes('%')) {
                            const value = parseFloat(cellData.replace('%', ''));
                            if (!isNaN(value) && value >= 0 && value <= 100) {
                                dailyEntry.dates[dateKey] = value;
                            }
                        }
                    }

                    result.dailyDSAT.push(dailyEntry);
                }
            }
        }
        // Parse MTD data lines
        else if (currentSection === 'mtd') {
            // Skip header lines
            if (line.toLowerCase().includes('dsat') && line.toLowerCase().includes('aht')) {
                continue;
            }

            // Split by tabs for MTD data
            const parts = line.split(/\t+/);
            if (parts.length >= 3) {
                const name = parts[0].trim().replace(/,$/, '');
                const dsatStr = parts[1].trim().replace('%', '');
                const ahtStr = parts[2].trim();

                const dsat = parseFloat(dsatStr);
                const aht = parseFloat(ahtStr);

                if (name && !isNaN(dsat) && !isNaN(aht) && dsat >= 0 && dsat <= 100 && aht > 0) {
                    result.mtdRanking.push({
                        name,
                        dsat,
                        aht,
                        qa: null
                    });
                }
            }
        }
    }

    return result;
}

export function parseKPI(raw: string): ParsedData {
    const format = detectFormat(raw);

    if (format === 'mixed') {
        return parseMixedFormat(raw);
    } else if (format === 'three-column') {
        return parseThreeColumnFormat(raw);
    } else {
        return parseTabularFormat(raw);
    }
}


