"use client";
import React from "react";
import { useDropzone } from "react-dropzone";
import clsx from "clsx";
import { parseXLSXAutoEnhanced, convertToLegacyFormat, DailyDSATRecord } from "./lib/parseNewXLSX";
import { ParsedData, MTDRanking } from "./lib/parseKPI";

interface FileDropProps {
    onFile: (text: string) => void;
    onNewFormatData?: (data: ParsedData) => void;
    onEnhancedFileUpload?: (data: ParsedData, fileType: 'mtd' | 'aht' | 'qa' | 'apm-data' | 'bi-data' | 'mtd-bi-data') => void;
}

export const FileDrop: React.FC<FileDropProps> = ({ onFile, onNewFormatData, onEnhancedFileUpload }) => {
    const processFile = React.useCallback((file: File) => {

        // Check if it's an Excel file
        if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
            const reader = new FileReader();
            reader.onload = () => {
                if (reader.result instanceof ArrayBuffer) {
                    try {
                        // Parse with enhanced auto-detection
                        const parsedFileData = parseXLSXAutoEnhanced(reader.result, file.name);

                        if (parsedFileData.type === 'mtd' && parsedFileData.mtdData) {
                            // Handle MTD DSAT file - cast to proper type since we know the structure
                            const mtdAgentData = parsedFileData.mtdData as Array<{ agent?: string; name?: string; mtdDSAT?: number; dsat?: number; aht?: number; qa?: number; totalSurveys?: number; surveys?: number; totalPositiveSurveys?: number; positiveSurveys?: number; totalNegativeSurveys?: number; negativeSurveys?: number }>;
                            const compatibleData: ParsedData = {
                                dailyDSAT: [],
                                mtdRanking: mtdAgentData.map(agent => ({
                                    name: agent.agent || agent.name || "Unknown",
                                    dsat: agent.mtdDSAT || agent.dsat || 0,
                                    aht: agent.aht || 0,
                                    qa: agent.qa || null,
                                    surveys: agent.totalSurveys || agent.surveys,
                                    positiveSurveys: agent.totalPositiveSurveys || agent.positiveSurveys,
                                    negativeSurveys: agent.totalNegativeSurveys || agent.negativeSurveys
                                })),
                                dates: []
                            };

                            if (onEnhancedFileUpload) {
                                onEnhancedFileUpload(compatibleData, 'mtd');
                            } else if (onNewFormatData) {
                                onNewFormatData(compatibleData);
                            }
                        } else if (parsedFileData.type === 'aht' && parsedFileData.ahtData) {
                            // Handle AHT file
                            const compatibleData: ParsedData = {
                                dailyDSAT: [],
                                mtdRanking: parsedFileData.ahtData.map(agent => ({
                                    name: agent.agent,
                                    dsat: 0, // No DSAT data in AHT files, will use default
                                    aht: agent.aht || 0, // AHT data from AHT file
                                    qa: null, // No QA data in AHT files
                                    surveys: agent.totalSurveys,
                                    positiveSurveys: agent.totalPositiveSurveys,
                                    negativeSurveys: agent.totalNegativeSurveys
                                })),
                                dates: []
                            };

                            if (onEnhancedFileUpload) {
                                onEnhancedFileUpload(compatibleData, 'aht');
                            } else if (onNewFormatData) {
                                onNewFormatData(compatibleData);
                            }
                        } else if (parsedFileData.type === 'qa' && parsedFileData.qaData) {
                            // Handle QA file
                            const compatibleData: ParsedData = {
                                dailyDSAT: [],
                                mtdRanking: parsedFileData.qaData.map(agent => ({
                                    name: agent.agent,
                                    dsat: 0, // No DSAT data in QA files, will use default
                                    aht: null, // No AHT data in QA files
                                    qa: agent.qa || null, // QA data from QA file
                                    surveys: agent.totalSurveys,
                                    positiveSurveys: agent.totalPositiveSurveys,
                                    negativeSurveys: agent.totalNegativeSurveys
                                })),
                                dates: []
                            };

                            if (onEnhancedFileUpload) {
                                onEnhancedFileUpload(compatibleData, 'qa');
                            } else if (onNewFormatData) {
                                onNewFormatData(compatibleData);
                            }
                        } else if (parsedFileData.type === 'bi-data' && parsedFileData.dailyData) {
                            // Handle Daily BI Data file
                            // Daily BI Data format detected

                            // Process the data into the required format
                            const legacyFormat = convertToLegacyFormat(parsedFileData.dailyData);

                            const compatibleData: ParsedData & {
                                rawDailySurveyData?: Array<{ date: string; agent: string; dsat: number | null; surveys: number }>;
                                teamPerformanceData?: { [date: string]: number | null };
                            } = {
                                dailyDSAT: legacyFormat.dailyDSAT,
                                mtdRanking: legacyFormat.mtdData.map(agent => ({
                                    name: agent.name,
                                    dsat: agent.dsat || 0,
                                    aht: 0, // Default value since BI format doesn't include AHT
                                    qa: null, // Default value since BI format doesn't include QA
                                    surveys: agent.totalSurveys,
                                    positiveSurveys: agent.positiveSurveys,
                                    negativeSurveys: agent.negativeSurveys
                                })),
                                dates: legacyFormat.dates,
                                rawDailySurveyData: parsedFileData.dailyData.records.map(record => ({
                                    date: record.date,
                                    agent: record.agent,
                                    dsat: record.dsat,
                                    surveys: record.surveys
                                })),
                                teamPerformanceData: parsedFileData.dailyData.teamPerformanceData
                            };

                            if (onEnhancedFileUpload) {
                                onEnhancedFileUpload(compatibleData, 'bi-data');
                            } else if (onNewFormatData) {
                                onNewFormatData(compatibleData);
                            }
                        } else if (parsedFileData.type === 'mtd-bi-data' && parsedFileData.mtdData) {
                            // Handle MTD BI Data file
                            // MTD BI Data format detected

                            // Create a processed data structure with MTD data only
                            const processedData: ParsedData & { mtdTeamPerformance?: { dsat: number; aht: number; qa?: number } } = {
                                dailyDSAT: [], // No daily data for MTD BI format
                                mtdRanking: parsedFileData.mtdData as MTDRanking[],
                                dates: [], // No daily dates for MTD BI format
                                mtdTeamPerformance: parsedFileData.mtdTeamPerformance || undefined
                            };

                            if (onEnhancedFileUpload) {
                                onEnhancedFileUpload(processedData, 'mtd-bi-data');
                            } else if (onNewFormatData) {
                                onNewFormatData(processedData);
                            }
                        } else if (parsedFileData.dailyData && parsedFileData.dailyData.records.length > 0) {
                            // Handle APM Data file
                            // Raw daily survey data format detected

                            // Log survey data by date
                            const surveysByDate: { [date: string]: { totalSurveys: number; agents: number } } = {};
                            parsedFileData.dailyData.records.forEach(record => {
                                if (!surveysByDate[record.date]) {
                                    surveysByDate[record.date] = { totalSurveys: 0, agents: 0 };
                                }
                                surveysByDate[record.date].totalSurveys += record.surveys;
                                surveysByDate[record.date].agents += 1;
                            });

                            // Survey data aggregation completed

                            const legacyFormat = convertToLegacyFormat(parsedFileData.dailyData);

                            const compatibleData: ParsedData & { rawDailySurveyData?: Array<{ date: string; agent: string; dsat: number | null; surveys: number }> } = {
                                dailyDSAT: legacyFormat.dailyDSAT,
                                mtdRanking: legacyFormat.mtdData.map(agent => ({
                                    name: agent.name,
                                    dsat: agent.dsat || 0,
                                    aht: 0, // Default value since new format doesn't include AHT
                                    qa: null, // Default value since new format doesn't include QA
                                    surveys: agent.totalSurveys,
                                    positiveSurveys: agent.positiveSurveys,
                                    negativeSurveys: agent.negativeSurveys
                                })),
                                dates: legacyFormat.dates,
                                rawDailySurveyData: parsedFileData.dailyData.records.map(record => ({
                                    date: record.date,
                                    agent: record.agent,
                                    dsat: record.dsat,
                                    surveys: record.surveys
                                }))
                            };

                            if (onEnhancedFileUpload) {
                                onEnhancedFileUpload(compatibleData, parsedFileData.type);
                            } else if (onNewFormatData) {
                                onNewFormatData(compatibleData);
                            } else {
                                // Fallback: convert to CSV text format
                                let csvText = "Date,Agent Name,DSAT Value\n";
                                parsedFileData.dailyData.records.forEach((record: DailyDSATRecord) => {
                                    csvText += `${record.date},${record.agent},${record.dsat || ''}\n`;
                                });
                                onFile(csvText);
                            }
                        } else {
                            console.error("No data found in Excel file");
                        }
                    } catch (error) {
                        console.error("Error parsing Excel file:", error);
                    }
                }
            };
            reader.readAsArrayBuffer(file);
        } else {
            // Handle text files as before
            const reader = new FileReader();
            reader.onload = () => {
                if (typeof reader.result === "string") {
                    onFile(reader.result);
                }
            };
            reader.readAsText(file);
        }
    }, [onFile, onNewFormatData, onEnhancedFileUpload]);

    const onDrop = React.useCallback((acceptedFiles: File[]) => {
        if (acceptedFiles.length === 0) return;

        // Processing uploaded files

        // Process each file individually
        acceptedFiles.forEach((file) => {
            processFile(file);
        });
    }, [processFile]);

    const { getRootProps, getInputProps, isDragActive } = useDropzone({
        onDrop,
        accept: {
            "text/plain": [".txt"],
            "text/markdown": [".md"],
            "text/csv": [".csv"],
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [".xlsx"],
            "application/vnd.ms-excel": [".xls"]
        },
        multiple: true, // Enable multiple file uploads
    });

    return (
        <div
            {...getRootProps()}
            className={clsx(
                "relative group cursor-pointer transition-all duration-300 ease-in-out",
                "border-2 border-dashed rounded-2xl p-12",
                "bg-white dark:bg-gray-800 shadow-sm hover:shadow-md",
                "transform hover:scale-[1.02] active:scale-[0.98]",
                isDragActive
                    ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20 shadow-lg scale-[1.02]"
                    : "border-gray-300 dark:border-gray-600 hover:border-blue-400 dark:hover:border-blue-500"
            )}
        >
            <input {...getInputProps()} />

            {/* Upload Icon */}
            <div className={clsx(
                "mx-auto w-16 h-16 mb-6 rounded-full flex items-center justify-center transition-all duration-300",
                isDragActive
                    ? "bg-blue-500 text-white"
                    : "bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 group-hover:bg-blue-100 dark:group-hover:bg-blue-900/30 group-hover:text-blue-500"
            )}>
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                        d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
            </div>

            {/* Content */}
            <div className="text-center">
                {isDragActive ? (
                    <div>
                        <p className="text-lg font-semibold text-blue-600 dark:text-blue-400 mb-2">
                            Drop your files here!
                        </p>
                        <p className="text-sm text-blue-500 dark:text-blue-300">
                            Release to upload your KPI data
                        </p>
                    </div>
                ) : (
                    <div>
                        <p className="text-lg font-semibold text-gray-700 dark:text-gray-200 mb-2">
                            Upload KPI Data
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                            Drag & drop your files here, or click to browse<br />
                            <span className="text-xs">💡 Tip: Upload multiple files at once (MTD + AHT + QA + Daily)</span>
                        </p>
                        <div className="inline-flex items-center px-4 py-2 bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-lg text-sm font-medium border border-blue-200 dark:border-blue-700">
                            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            Supported: .txt, .csv, .md, .xlsx, .xls
                        </div>
                    </div>
                )}
            </div>

            {/* Background decoration */}
            <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-500/5 to-purple-500/5 dark:from-blue-400/5 dark:to-purple-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
        </div>
    );
};
