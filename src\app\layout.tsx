import "./globals.css";
import { Inter } from "next/font/google";
import ThemeShell from "@/app/components/ThemeShell";

const inter = Inter({ subsets: ["latin"] });

export const metadata = {
  title: "KPI Dashboard",
  description: "Dynamic KPI poster generator",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="dark" suppressHydrationWarning>
      <body
        className={`${inter.className} bg-gray-900 text-gray-100`}
        suppressHydrationWarning
      >
        <ThemeShell>{children}</ThemeShell>
      </body>
    </html>
  );
}
