import React, { useRef, useState } from "react";
import { toPng } from "html-to-image";
import { MTDRanking } from "@/app/lib/parseKPI";
import clsx from "clsx";
import { convertDecimalMinutesToTime } from "@/app/lib/parseNewXLSX";
import { useDepartment } from './DepartmentContext';

interface MTDRankingPosterProps {
    data: MTDRanking[];
    filteredData?: MTDRanking[];
    searchTerm?: string;
    filterPerformance?: string;
    selectedAgent?: string;
    filteredAndSortedData?: MTDRanking[];
    teamPerformanceData?: {
        dsat: number;
        aht: number;
        qa: number;
        isWeighted?: boolean;
    };
    performanceStats?: {
        excellent: number;
        good: number;
        warning: number;
        poor: number;
    };
}

function getPerformanceColor(score: number | null, excellent: number, good: number, target: number, type: "dsat" | "aht"): string {
    if (score === null) return "text-gray-400 dark:text-gray-500";

    if (type === "dsat") {
        if (score <= excellent) return "text-green-600 dark:text-green-400";
        if (score <= good) return "text-yellow-600 dark:text-yellow-400";
        if (score <= target) return "text-orange-600 dark:text-orange-400";
        return "text-red-500 dark:text-red-400";
    } else if (type === "aht") {
        if (score <= excellent) return "text-green-600 dark:text-green-400";
        if (score <= good) return "text-yellow-600 dark:text-yellow-400";
        if (score <= target) return "text-orange-600 dark:text-orange-400";
        return "text-red-500 dark:text-red-400";
    }

    return "text-gray-600 dark:text-gray-400";
}

function getPerformanceBg(score: number | null, excellent: number, good: number, target: number, type: "dsat" | "aht"): string {
    if (score === null) return "bg-gray-50 dark:bg-gray-800";

    if (type === "dsat") {
        if (score <= excellent) return "bg-green-50 dark:bg-green-900/20";
        if (score <= good) return "bg-yellow-50 dark:bg-yellow-900/20";
        if (score <= target) return "bg-orange-50 dark:bg-orange-900/20";
        return "bg-red-50 dark:bg-red-900/20";
    } else if (type === "aht") {
        if (score <= excellent) return "bg-green-50 dark:bg-green-900/20";
        if (score <= good) return "bg-yellow-50 dark:bg-yellow-900/20";
        if (score <= target) return "bg-orange-50 dark:bg-orange-900/20";
        return "bg-red-50 dark:bg-red-900/20";
    }

    return "bg-gray-50 dark:bg-gray-800";
}

function getQAPerformanceColor(score: number | null, excellent: number, target: number): string {
    if (score === null) return "text-gray-400 dark:text-gray-500";
    if (score >= excellent) return "text-green-600 dark:text-green-400";
    if (score >= target) return "text-yellow-600 dark:text-yellow-400";
    return "text-red-500 dark:text-red-400";
}

function getQAPerformanceBg(score: number | null, excellent: number, target: number): string {
    if (score === null) return "bg-gray-50 dark:bg-gray-800";
    if (score >= excellent) return "bg-green-50 dark:bg-green-900/20";
    if (score >= target) return "bg-yellow-50 dark:bg-yellow-900/20";
    return "bg-red-50 dark:bg-red-900/20";
}

function getRankingIcon(rank: number): string {
    if (rank === 1) return "🥇";
    if (rank === 2) return "🥈";
    if (rank === 3) return "🥉";
    return `#${rank}`;
}

export const MTDRankingPoster: React.FC<MTDRankingPosterProps> = ({
    data,
    filteredData,
    searchTerm,
    filterPerformance,
    selectedAgent,
    filteredAndSortedData,
    teamPerformanceData: preCalculatedTeamData,
    performanceStats: preCalculatedStats
}) => {
    const ref = useRef<HTMLDivElement>(null);
    const { config } = useDepartment();
    const [isDownloading, setIsDownloading] = useState(false);

    // Use the exact same data from the table
    const displayData = filteredAndSortedData || filteredData || data;
    const todayDate = new Date().toLocaleDateString();

    // Use pre-calculated team performance data directly from table - NO CALCULATIONS
    const teamPerformanceData = preCalculatedTeamData || { dsat: 0, aht: 0, qa: 0 };

    // Use the exact same ranked data from the table - no additional ranking needed
    const rankedData = displayData;

    const handleDownload = async () => {
        if (!ref.current || isDownloading) return;

        setIsDownloading(true);
        try {
            console.log('Starting MTD ranking poster download...');
            const dataUrl = await toPng(ref.current, {
                pixelRatio: 2,
                backgroundColor: '#ffffff',
                style: {
                    // Override any problematic CSS properties
                    transform: 'scale(1)',
                    transformOrigin: 'top left'
                },
                filter: (node) => {
                    // Skip elements that might cause issues
                    if (node.classList?.contains('ignore-screenshot')) {
                        return false;
                    }
                    return true;
                }
            });
            console.log('Poster rendered, creating download link...');

            const link = document.createElement("a");
            let fileName = `mtd-ranking-${todayDate}.png`;
            if (searchTerm) fileName = `mtd-ranking-filtered-${searchTerm}-${todayDate}.png`;
            if (filterPerformance && filterPerformance !== "all") fileName = `mtd-ranking-${filterPerformance}-${todayDate}.png`;
            link.download = fileName;
            link.href = dataUrl;

            // Add to DOM temporarily to ensure it works across browsers
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            console.log('MTD ranking download triggered successfully');
        } catch (error) {
            console.error('Error downloading MTD ranking poster:', error);
            // Provide user feedback with more specific error information
            const errorMessage = error instanceof Error ? error.message : String(error);
            if (errorMessage.includes('oklch') || errorMessage.includes('color')) {
                alert('Download failed due to color compatibility issues. This is a known issue with certain CSS color functions. Please try using a different browser or contact support.');
            } else {
                alert('Failed to download poster. Please try again or check the console for more details.');
            }
        } finally {
            setIsDownloading(false);
        }
    };

    // Use pre-calculated statistics directly from table - NO CALCULATIONS
    const stats = preCalculatedStats || { excellent: 0, good: 0, warning: 0, poor: 0 };

    return (
        <div className="relative group">
            <div
                ref={ref}
                className="relative w-[1200px] min-h-[1600px] bg-white dark:bg-gray-900 rounded-3xl shadow-2xl overflow-hidden border border-gray-200 dark:border-gray-700"
            >
                {/* Background Pattern */}
                <div className="absolute inset-0 opacity-30">
                    <div className="absolute inset-0 bg-gradient-to-br from-purple-50 via-indigo-50 to-blue-50 dark:from-purple-900/10 dark:via-indigo-900/10 dark:to-blue-900/10"></div>
                </div>

                <div className="relative h-full flex flex-col p-8">
                    {/* Header */}
                    <div className="relative mb-8">
                        <div className="bg-gradient-to-r from-purple-600 via-indigo-600 to-blue-600 rounded-2xl p-6 text-center shadow-lg">
                            <h1 className="text-5xl font-black text-white mb-2 tracking-tight">
                                MTD Performance Ranking
                            </h1>
                            <div className="w-20 h-1 bg-white/30 mx-auto mb-3 rounded-full"></div>
                            <p className="text-lg text-purple-100 font-medium">
                                Monthly Leaderboard • {todayDate}
                                {searchTerm && (
                                    <span className="block text-sm mt-1 text-purple-200">
                                        Filtered: &ldquo;{searchTerm}&rdquo;
                                    </span>
                                )}
                                {filterPerformance && filterPerformance !== "all" && (
                                    <span className="block text-sm mt-1 text-purple-200">
                                        Performance: {filterPerformance}
                                    </span>
                                )}
                            </p>
                        </div>
                    </div>

                    {/* Statistics Cards */}
                    <div className="grid grid-cols-5 gap-2 mb-8">
                        <div className="bg-white/80 dark:bg-gray-800/80 rounded-xl p-4 text-center border border-gray-200 dark:border-gray-700">
                            <div className="text-2xl font-bold text-gray-700 dark:text-gray-200">{displayData.length}</div>
                            <div className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide">Total</div>
                        </div>
                        <div className="bg-green-50 dark:bg-green-900/20 rounded-xl p-4 text-center border border-green-200 dark:border-green-700">
                            <div className="text-xl font-bold text-green-600 dark:text-green-400">{stats.excellent}</div>
                            <div className="text-xs text-green-600 dark:text-green-400 uppercase tracking-wide">Tier 1</div>
                            <div className="text-xs text-green-500 dark:text-green-400 opacity-75">≤{config.dsat.excellent}% • ≤9min</div>
                        </div>
                        <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-xl p-4 text-center border border-yellow-200 dark:border-yellow-700">
                            <div className="text-xl font-bold text-yellow-600 dark:text-yellow-400">{stats.good}</div>
                            <div className="text-xs text-yellow-600 dark:text-yellow-400 uppercase tracking-wide">Tier 2</div>
                            <div className="text-xs text-yellow-500 dark:text-yellow-400 opacity-75">≤{config.dsat.good}% • ≤10min</div>
                        </div>
                        <div className="bg-orange-50 dark:bg-orange-900/20 rounded-xl p-4 text-center border border-orange-200 dark:border-orange-700">
                            <div className="text-xl font-bold text-orange-600 dark:text-orange-400">{stats.warning}</div>
                            <div className="text-xs text-orange-600 dark:text-orange-400 uppercase tracking-wide">Tier 3</div>
                            <div className="text-xs text-orange-500 dark:text-orange-400 opacity-75">≤{config.dsat.target}% • ≤11min</div>
                        </div>
                        <div className="bg-red-50 dark:bg-red-900/20 rounded-xl p-4 text-center border border-red-200 dark:border-red-700">
                            <div className="text-xl font-bold text-red-600 dark:text-red-400">{stats.poor}</div>
                            <div className="text-xs text-red-600 dark:text-red-400 uppercase tracking-wide">Above T3</div>
                            <div className="text-xs text-red-500 dark:text-red-400 opacity-75">&gt;{config.dsat.target}% or &gt;11min</div>
                        </div>
                    </div>

                    {/* Team Performance Section - Updated Style */}
                    <div className="mb-8">
                        <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-4 text-center flex items-center justify-center gap-2">
                            <span className="text-lg">👑</span> Team Performance
                        </h2>
                        <div className="bg-gradient-to-r from-purple-500/10 to-blue-500/10 dark:from-purple-900/40 dark:to-blue-900/40 rounded-xl p-4 border border-purple-200 dark:border-purple-700">
                            <div className="grid grid-cols-3 gap-6">
                                <div className="bg-white/90 dark:bg-gray-800/90 rounded-lg p-4 text-center border border-gray-200 dark:border-gray-700">
                                    <div className={clsx(
                                        "text-3xl font-bold mb-1",
                                        getPerformanceColor(teamPerformanceData.dsat, config.dsat.excellent, config.dsat.good, config.dsat.target, "dsat")
                                    )}>
                                        {teamPerformanceData.dsat.toFixed(2)}%
                                    </div>
                                    <div className="text-sm text-gray-600 dark:text-gray-400">Team DSAT</div>
                                </div>
                                <div className="bg-white/90 dark:bg-gray-800/90 rounded-lg p-4 text-center border border-gray-200 dark:border-gray-700">
                                    <div className={clsx(
                                        "text-3xl font-bold mb-1",
                                        getPerformanceColor(teamPerformanceData.aht, config.aht.excellent, config.aht.good, config.aht.target, "aht")
                                    )}>
                                        {convertDecimalMinutesToTime(teamPerformanceData.aht)}
                                    </div>
                                    <div className="text-sm text-gray-600 dark:text-gray-400">Team AHT</div>
                                </div>
                                <div className="bg-white/90 dark:bg-gray-800/90 rounded-lg p-4 text-center border border-gray-200 dark:border-gray-700">
                                    <div className={clsx(
                                        "text-3xl font-bold mb-1",
                                        getQAPerformanceColor(teamPerformanceData.qa, config.qa.excellent, config.qa.target)
                                    )}>
                                        {teamPerformanceData.qa ? teamPerformanceData.qa.toFixed(2) + "%" : "-"}
                                    </div>
                                    <div className="text-sm text-gray-600 dark:text-gray-400">Team QA</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Top Performers - Use exact data from table */}
                    <div className="mb-6">
                        <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-4 text-center">
                            🏆 Top Performers
                        </h2>
                        <div className="grid grid-cols-3 gap-4">
                            {rankedData.slice(0, 3).map((agent, index) => (
                                <div key={agent.name} className="bg-white/80 dark:bg-gray-800/80 rounded-xl p-4 text-center border border-gray-200 dark:border-gray-700">
                                    <div className="text-3xl mb-2">{getRankingIcon(index + 1)}</div>
                                    <div className="font-bold text-gray-800 dark:text-gray-200 text-sm mb-1 truncate">{agent.name}</div>
                                    <div className="text-xs text-gray-600 dark:text-gray-400">
                                        DSAT: <span className={getPerformanceColor(agent.dsat, config.dsat.excellent, config.dsat.good, config.dsat.target, "dsat")}>{agent.dsat.toFixed(2)}%</span>
                                        {" • "}
                                        AHT: <span className={getPerformanceColor(agent.aht, config.aht.excellent, config.aht.good, config.aht.target, "aht")}>{convertDecimalMinutesToTime(agent.aht || 0)}</span>
                                        {agent.qa !== null && (
                                            <>
                                                {" • "}
                                                QA: <span className={getQAPerformanceColor(agent.qa, config.qa.excellent, config.qa.target)}>{agent.qa.toFixed(2)}%</span>
                                            </>
                                        )}
                                    </div>
                                    {agent.surveys !== undefined && agent.surveys > 0 && (
                                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                            Surveys: {agent.surveys}
                                        </div>
                                    )}
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Focused Agent Card - Use exact data from table */}
                    {selectedAgent && selectedAgent !== "all" && (
                        <div className="mb-6">
                            <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-4 text-center">
                                🔍 Focused Agent
                            </h2>
                            <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 dark:from-blue-900/40 dark:to-purple-900/40 rounded-xl p-6 border border-blue-200 dark:border-blue-700">
                                {rankedData.filter(a => a.name === selectedAgent).map(agent => (
                                    <div key={agent.name} className="flex flex-col items-center">
                                        <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-2xl mb-3">
                                            {agent.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                                        </div>
                                        <div className="text-xl font-bold text-gray-800 dark:text-gray-200 mb-1">{agent.name}</div>
                                        <div className="text-sm text-gray-600 dark:text-gray-400 mb-4">Rank: {getRankingIcon(rankedData.indexOf(agent) + 1)}</div>

                                        <div className="grid grid-cols-3 gap-4 w-full">
                                            <div className={clsx(
                                                "rounded-xl p-4 text-center",
                                                getPerformanceBg(agent.dsat, config.dsat.excellent, config.dsat.good, config.dsat.target, "dsat")
                                            )}>
                                                <div className={clsx(
                                                    "text-2xl font-bold",
                                                    getPerformanceColor(agent.dsat, config.dsat.excellent, config.dsat.good, config.dsat.target, "dsat")
                                                )}>
                                                    {agent.dsat.toFixed(2)}%
                                                </div>
                                                <div className="text-sm text-gray-600 dark:text-gray-400">DSAT</div>
                                            </div>
                                            <div className={clsx(
                                                "rounded-xl p-4 text-center",
                                                getPerformanceBg(agent.aht, config.aht.excellent, config.aht.good, config.aht.target, "aht")
                                            )}>
                                                <div className={clsx(
                                                    "text-2xl font-bold",
                                                    getPerformanceColor(agent.aht, config.aht.excellent, config.aht.good, config.aht.target, "aht")
                                                )}>
                                                    {convertDecimalMinutesToTime(agent.aht || 0)}
                                                </div>
                                                <div className="text-sm text-gray-600 dark:text-gray-400">AHT</div>
                                            </div>
                                            <div className={clsx(
                                                "rounded-xl p-4 text-center",
                                                getQAPerformanceBg(agent.qa, config.qa.excellent, config.qa.target)
                                            )}>
                                                <div className={clsx(
                                                    "text-2xl font-bold",
                                                    getQAPerformanceColor(agent.qa, config.qa.excellent, config.qa.target)
                                                )}>
                                                    {agent.qa !== null ? `${agent.qa.toFixed(2)}%` : '-'}
                                                </div>
                                                <div className="text-sm text-gray-600 dark:text-gray-400">QA</div>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}

                    {/* Full Rankings Table - Use exact data from table */}
                    <div className="flex-1 bg-white/80 dark:bg-gray-800/80 rounded-2xl border border-gray-200 dark:border-gray-700 overflow-hidden">
                        <div className="p-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
                            <h3 className="font-bold text-gray-800 dark:text-gray-200">Full Rankings</h3>
                            <p className="text-xs text-gray-600 dark:text-gray-400">
                                DSAT Target: ≤{config.dsat.target}% • AHT Target: ≤{config.aht.excellent} min • QA Target: ≥{config.qa.target}%
                            </p>
                        </div>
                        <div className="max-h-[680px]">
                            <table className="w-full text-sm">
                                <thead className="bg-gray-50 dark:bg-gray-900 sticky top-0">
                                    <tr>
                                        <th className="px-2 py-2 text-center font-bold text-gray-700 dark:text-gray-200 border-b border-gray-200 dark:border-gray-700">Rank</th>
                                        <th className="px-3 py-2 text-left font-bold text-gray-700 dark:text-gray-200 border-b border-gray-200 dark:border-gray-700">Agent</th>
                                        <th className="px-2 py-2 text-center font-bold text-gray-700 dark:text-gray-200 border-b border-gray-200 dark:border-gray-700">DSAT</th>
                                        <th className="px-2 py-2 text-center font-bold text-gray-700 dark:text-gray-200 border-b border-gray-200 dark:border-gray-700">AHT</th>
                                        <th className="px-2 py-2 text-center font-bold text-gray-700 dark:text-gray-200 border-b border-gray-200 dark:border-gray-700">QA</th>
                                        <th className="px-2 py-2 text-center font-bold text-gray-700 dark:text-gray-200 border-b border-gray-200 dark:border-gray-700">Surveys</th>
                                        <th className="px-2 py-2 text-center font-bold text-gray-700 dark:text-gray-200 border-b border-gray-200 dark:border-gray-700">Performance</th>
                                    </tr>
                                </thead>
                                <tbody className="text-xs">
                                    {/* Display all agents exactly as in the table */}
                                    {rankedData.map((agent, index) => (
                                        <tr key={agent.name} className={index % 2 === 0 ? 'bg-gray-50/50 dark:bg-gray-800/50' : ''}>
                                            <td className="px-2 py-1 text-center">
                                                <span>{getRankingIcon(index + 1)}</span>
                                            </td>
                                            <td className="px-3 py-1 min-w-[140px]">
                                                <div className="flex items-center">
                                                    <div className="w-5 h-5 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center text-purple-600 dark:text-purple-300 font-bold text-xs mr-2 flex-shrink-0">
                                                        {agent.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                                                    </div>
                                                    <span className="font-medium text-gray-900 dark:text-gray-100 truncate" title={agent.name}>{agent.name}</span>
                                                </div>
                                            </td>
                                            <td className={clsx(
                                                "px-2 py-1 text-center",
                                                getPerformanceBg(agent.dsat, config.dsat.excellent, config.dsat.good, config.dsat.target, "dsat")
                                            )}>
                                                <span className={clsx("font-bold", getPerformanceColor(agent.dsat, config.dsat.excellent, config.dsat.good, config.dsat.target, "dsat"))}>
                                                    {agent.dsat.toFixed(2)}%
                                                </span>
                                            </td>
                                            <td className={clsx(
                                                "px-2 py-1 text-center",
                                                getPerformanceBg(agent.aht, config.aht.excellent, config.aht.good, config.aht.target, "aht")
                                            )}>
                                                <span className={clsx("font-bold", getPerformanceColor(agent.aht, config.aht.excellent, config.aht.good, config.aht.target, "aht"))}>
                                                    {convertDecimalMinutesToTime(agent.aht || 0)}
                                                </span>
                                            </td>
                                            <td className={clsx(
                                                "px-2 py-1 text-center",
                                                getQAPerformanceBg(agent.qa, config.qa.excellent, config.qa.target)
                                            )}>
                                                <span className={clsx("font-bold", getQAPerformanceColor(agent.qa, config.qa.excellent, config.qa.target))}>
                                                    {agent.qa !== null ? `${agent.qa.toFixed(2)}%` : '-'}
                                                </span>
                                            </td>
                                            <td className="px-2 py-1 text-center">
                                                <span className="text-gray-600 dark:text-gray-400 font-medium">
                                                    {agent.surveys !== undefined ? agent.surveys : '-'}
                                                </span>
                                            </td>
                                            <td className="px-2 py-1 text-center">
                                                <span className="text-gray-600 dark:text-gray-400 font-medium">
                                                    {/* Performance summary based on DSAT + AHT */}
                                                    {agent.dsat <= config.dsat.excellent && (agent.aht || 0) <= 9 ? "Excellent" :
                                                        agent.dsat <= config.dsat.target && (agent.aht || 0) <= 11 ? "Good" : "Focus"}
                                                </span>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    {/* Footer */}
                    <div className="mt-6 text-center space-y-3">
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                            Generated: {todayDate} • Agents: {displayData.length}/{data.length}
                        </div>
                        <button
                            onClick={handleDownload}
                            disabled={isDownloading}
                            className={`inline-flex items-center gap-2 font-bold px-6 py-3 rounded-xl shadow-lg transition-all duration-300 text-white ${isDownloading
                                ? 'bg-gray-500 cursor-not-allowed'
                                : 'bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 hover:shadow-xl transform hover:scale-105'
                                }`}
                        >
                            {isDownloading ? (
                                <>
                                    <span className="animate-spin inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full"></span>
                                    Generating...
                                </>
                            ) : (
                                <>
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    Download MTD Ranking Report
                                </>
                            )}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
}; 