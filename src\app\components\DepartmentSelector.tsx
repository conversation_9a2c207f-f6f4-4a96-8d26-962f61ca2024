"use client";
import React, { useState, useEffect } from 'react';
import { useDepartment, Department } from './DepartmentContext';
import clsx from 'clsx';

export const DepartmentSelector: React.FC = () => {
    const { department, setDepartment, config } = useDepartment();
    const [isHydrated, setIsHydrated] = useState(false);

    // Prevent hydration mismatch by only showing dynamic content after hydration
    useEffect(() => {
        setIsHydrated(true);
    }, []);

    const departments: { value: Department; label: string; icon: string }[] = [
        { value: 'customer-support', label: 'Customer Support', icon: '🎧' },
        { value: 'inbound-concierge', label: 'Inbound Concierge', icon: '🏨' }
    ];

    return (
        <div className="relative">
            <label htmlFor="department-select" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Department
            </label>
            <div className="relative">
                <select
                    id="department-select"
                    value={department}
                    onChange={(e) => setDepartment(e.target.value as Department)}
                    className={clsx(
                        "appearance-none relative w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600",
                        "rounded-lg pl-10 pr-10 py-3 text-left cursor-pointer",
                        "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                        "text-gray-900 dark:text-gray-100 text-sm font-medium",
                        "shadow-sm hover:shadow-md transition-all duration-200"
                    )}
                >
                    {departments.map((dept) => (
                        <option key={dept.value} value={dept.value}>
                            {dept.label}
                        </option>
                    ))}
                </select>

                {/* Icon */}
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span className="text-lg">
                        {departments.find(d => d.value === department)?.icon}
                    </span>
                </div>

                {/* Dropdown Arrow */}
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <svg className="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                </div>
            </div>

            {/* Current Config Info - Only show after hydration to prevent mismatch */}
            {isHydrated && (
                <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                    DSAT: ≤{config.dsat.target}% • AHT: ≤{config.aht.target} min • QA: ≥{config.qa.target}%
                </div>
            )}
        </div>
    );
}; 