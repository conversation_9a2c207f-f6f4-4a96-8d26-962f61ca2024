"use client";
import React, { useState, useMemo, useEffect, useRef } from "react";
import { MTDRanking } from "@/app/lib/parseKPI";
import { MTDRankingPoster } from "./MTDRankingPoster";
import clsx from "clsx";
import { convertDecimalMinutesToTime } from '../lib/parseNewXLSX';
import { useDepartment } from './DepartmentContext';


interface MTDRankingTableProps {
    data: MTDRanking[];
    mtdTeamPerformance?: { dsat: number; aht: number; qa?: number } | null;
}

interface AgentMultiSelectProps {
    agents: string[];
    selectedAgents: string[];
    onSelectionChange: (selected: string[]) => void;
    performanceFilter: "all" | "excellent" | "good" | "warning" | "poor";
    onPerformanceFilterChange: (filter: "all" | "excellent" | "good" | "warning" | "poor") => void;
    performanceStats: {
        excellent: number;
        good: number;
        warning: number;
        poor: number;
    };
    config: {
        dsat: {
            excellent: number;
            good: number;
            target: number;
        };
    };
    data: MTDRanking[];
}

const AgentMultiSelect: React.FC<AgentMultiSelectProps> = ({
    agents,
    selectedAgents,
    onSelectionChange,
    performanceFilter,
    onPerformanceFilterChange,
    performanceStats,
    config,
    data
}) => {
    const [isOpen, setIsOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState("");
    const dropdownRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const handleToggleAgent = (agentName: string) => {
        if (selectedAgents.includes(agentName)) {
            onSelectionChange(selectedAgents.filter(name => name !== agentName));
        } else {
            onSelectionChange([...selectedAgents, agentName]);
        }
    };

    const handleSelectAll = () => {
        onSelectionChange(data.map(agent => agent.name));
    };

    const handleSelectNone = () => {
        onSelectionChange([]);
    };

    const handlePerformanceTierSelect = (tier: "all" | "excellent" | "good" | "warning" | "poor") => {
        if (tier === "all") {
            // Perform the same action as "Select All" button
            handleSelectAll();
        } else {
            // Find agents in the selected tier and replace the selection
            const agentsInTier = data.filter(agent => {
                const dsat = agent.dsat;
                const aht = agent.aht || 0;

                switch (tier) {
                    case "excellent":
                        return dsat <= config.dsat.excellent && aht <= 9; // Tier 1 performance
                    case "good":
                        return (dsat <= config.dsat.good && aht <= 10) && !(dsat <= config.dsat.excellent && aht <= 9); // Tier 2 performance
                    case "warning":
                        return (dsat <= config.dsat.target && aht <= 11) && !(dsat <= config.dsat.good && aht <= 10); // Tier 3 performance
                    case "poor":
                        return !((dsat <= config.dsat.target && aht <= 11)); // Above Tier 3 performance
                    default:
                        return false;
                }
            }).map(agent => agent.name);

            onSelectionChange(agentsInTier);
        }
        onPerformanceFilterChange(tier);
    };

    const handleAddTierAgents = (tier: "excellent" | "good" | "warning" | "poor") => {
        // Find agents in the selected tier and replace the selection (same as handlePerformanceTierSelect)
        const agentsInTier = data.filter(agent => {
            const dsat = agent.dsat;
            const aht = agent.aht || 0;

            switch (tier) {
                case "excellent":
                    return dsat <= config.dsat.excellent && aht <= 9; // Tier 1 performance
                case "good":
                    return (dsat <= config.dsat.good && aht <= 10) && !(dsat <= config.dsat.excellent && aht <= 9); // Tier 2 performance
                case "warning":
                    return (dsat <= config.dsat.target && aht <= 11) && !(dsat <= config.dsat.good && aht <= 10); // Tier 3 performance
                case "poor":
                    return !((dsat <= config.dsat.target && aht <= 11)); // Above Tier 3 performance
                default:
                    return false;
            }
        }).map(agent => agent.name);

        onSelectionChange(agentsInTier);
        onPerformanceFilterChange("all"); // Reset performance filter to "all" since we're using manual selection
    };

    const filteredAgents = searchTerm
        ? agents.filter(agent => agent.toLowerCase().includes(searchTerm.toLowerCase()))
        : agents;

    const getDisplayText = () => {
        const totalAgents = data.length;
        const hasAgentFiltering = selectedAgents.length < totalAgents;
        const hasPerformanceFiltering = performanceFilter !== "all";

        if (hasPerformanceFiltering && hasAgentFiltering) {
            const tierName = performanceFilter === "excellent" ? "Tier 1" :
                performanceFilter === "good" ? "Tier 2" :
                    performanceFilter === "warning" ? "Tier 3" : "Above T3";
            return `${tierName} + ${selectedAgents.length} agents`;
        } else if (hasPerformanceFiltering) {
            const tierName = performanceFilter === "excellent" ? "Tier 1" :
                performanceFilter === "good" ? "Tier 2" :
                    performanceFilter === "warning" ? "Tier 3" : "Above T3";
            return `${tierName} agents`;
        } else if (hasAgentFiltering) {
            if (selectedAgents.length === 0) return "No agents selected";
            if (selectedAgents.length === 1) return selectedAgents[0];
            return `${selectedAgents.length} of ${totalAgents} agents`;
        }
        return "All agents";
    };

    const getFilterSummary = () => {
        const totalAgents = data.length;
        const hasAgentFiltering = selectedAgents.length < totalAgents;
        const hasPerformanceFiltering = performanceFilter !== "all";

        if (!hasAgentFiltering && !hasPerformanceFiltering) {
            return `${totalAgents} agents`;
        }

        const parts = [];
        if (hasPerformanceFiltering) {
            const tierName = performanceFilter === "excellent" ? "Tier 1" :
                performanceFilter === "good" ? "Tier 2" :
                    performanceFilter === "warning" ? "Tier 3" : "Above T3";
            parts.push(tierName);
        }
        if (hasAgentFiltering) {
            parts.push(`${selectedAgents.length}/${totalAgents} agents`);
        }

        return parts.join(" • ");
    };

    return (
        <div className="relative" ref={dropdownRef}>
            <button
                onClick={() => setIsOpen(!isOpen)}
                className="flex items-center justify-between gap-2 px-3 py-1.5 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent min-w-[200px] max-w-[280px]"
                type="button"
            >
                <span className="flex-1 text-left truncate text-gray-900 dark:text-gray-100">
                    {getDisplayText()}
                </span>
                <svg
                    className={`w-4 h-4 text-gray-400 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
            </button>

            {isOpen && (
                <div className="absolute z-50 mt-1 w-[640px] bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg">
                    {/* Header */}
                    <div className="p-3 border-b border-gray-200 dark:border-gray-600">
                        <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                Agent & Performance Filter
                            </span>
                            <button
                                onClick={() => setIsOpen(false)}
                                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-1"
                                type="button"
                            >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                            {getFilterSummary()}
                        </div>
                        <div className="flex gap-2">
                            <button
                                onClick={handleSelectAll}
                                className="flex-1 px-2 py-1 text-xs bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 rounded hover:bg-blue-100 dark:hover:bg-blue-900/40 transition-colors"
                                type="button"
                            >
                                Select All
                            </button>
                            <button
                                onClick={() => {
                                    handleSelectNone();
                                    onPerformanceFilterChange("all");
                                }}
                                className="flex-1 px-2 py-1 text-xs bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 rounded hover:bg-red-100 dark:hover:bg-red-900/40 transition-colors"
                                type="button"
                            >
                                Clear All
                            </button>
                        </div>
                    </div>

                    {/* Horizontal Layout: Tiers Left, Agents Right */}
                    <div className="flex h-80">
                        {/* Performance Tiers Section - Left Panel */}
                        <div className="w-72 border-r border-gray-200 dark:border-gray-600 flex-shrink-0 flex flex-col">
                            <div className="p-3 border-b border-gray-200 dark:border-gray-600">
                                <div className="text-xs font-medium text-gray-600 dark:text-gray-400">PERFORMANCE TIERS</div>
                            </div>
                            <div className="flex-1 overflow-y-auto p-3">
                                <div className="space-y-2">
                                    <button
                                        onClick={() => handlePerformanceTierSelect("all")}
                                        className="w-full flex items-center p-3 hover:bg-blue-50 dark:hover:bg-blue-900/20 cursor-pointer transition-colors rounded-lg border border-blue-200 dark:border-blue-700 text-left"
                                        type="button"
                                    >
                                        <div className="flex-1">
                                            <div className="flex items-center gap-2 mb-1">
                                                <span className="w-3 h-3 rounded-full bg-blue-500"></span>
                                                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">All Performance Levels</span>
                                            </div>
                                            <div className="text-xs text-gray-500 dark:text-gray-400">Select all agents</div>
                                        </div>
                                        <span className="text-xs text-gray-500 dark:text-gray-400 ml-2 bg-gray-100 dark:bg-gray-600 px-2 py-1 rounded-full">
                                            {data.length}
                                        </span>
                                    </button>
                                    <button
                                        onClick={() => handleAddTierAgents("excellent")}
                                        className="w-full flex items-center p-3 hover:bg-green-50 dark:hover:bg-green-900/20 cursor-pointer transition-colors rounded-lg border border-green-200 dark:border-green-700 text-left"
                                        type="button"
                                    >
                                        <div className="flex-1">
                                            <div className="flex items-center gap-2 mb-1">
                                                <span className="w-3 h-3 rounded-full bg-green-500"></span>
                                                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">Tier 1</span>
                                            </div>
                                            <div className="text-xs text-gray-500 dark:text-gray-400">≤{config.dsat.excellent}% & ≤9min</div>
                                        </div>
                                        <span className="text-xs text-gray-500 dark:text-gray-400 ml-2 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-2 py-1 rounded-full">
                                            {performanceStats.excellent}
                                        </span>
                                    </button>
                                    <button
                                        onClick={() => handleAddTierAgents("good")}
                                        className="w-full flex items-center p-3 hover:bg-yellow-50 dark:hover:bg-yellow-900/20 cursor-pointer transition-colors rounded-lg border border-yellow-200 dark:border-yellow-700 text-left"
                                        type="button"
                                    >
                                        <div className="flex-1">
                                            <div className="flex items-center gap-2 mb-1">
                                                <span className="w-3 h-3 rounded-full bg-yellow-500"></span>
                                                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">Tier 2</span>
                                            </div>
                                            <div className="text-xs text-gray-500 dark:text-gray-400">≤{config.dsat.good}% & ≤10min</div>
                                        </div>
                                        <span className="text-xs text-gray-500 dark:text-gray-400 ml-2 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 px-2 py-1 rounded-full">
                                            {performanceStats.good}
                                        </span>
                                    </button>
                                    <button
                                        onClick={() => handleAddTierAgents("warning")}
                                        className="w-full flex items-center p-3 hover:bg-orange-50 dark:hover:bg-orange-900/20 cursor-pointer transition-colors rounded-lg border border-orange-200 dark:border-orange-700 text-left"
                                        type="button"
                                    >
                                        <div className="flex-1">
                                            <div className="flex items-center gap-2 mb-1">
                                                <span className="w-3 h-3 rounded-full bg-orange-500"></span>
                                                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">Tier 3</span>
                                            </div>
                                            <div className="text-xs text-gray-500 dark:text-gray-400">≤{config.dsat.target}% & ≤11min</div>
                                        </div>
                                        <span className="text-xs text-gray-500 dark:text-gray-400 ml-2 bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300 px-2 py-1 rounded-full">
                                            {performanceStats.warning}
                                        </span>
                                    </button>
                                    <button
                                        onClick={() => handleAddTierAgents("poor")}
                                        className="w-full flex items-center p-3 hover:bg-red-50 dark:hover:bg-red-900/20 cursor-pointer transition-colors rounded-lg border border-red-200 dark:border-red-700 text-left"
                                        type="button"
                                    >
                                        <div className="flex-1">
                                            <div className="flex items-center gap-2 mb-1">
                                                <span className="w-3 h-3 rounded-full bg-red-500"></span>
                                                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">Add Above T3</span>
                                            </div>
                                            <div className="text-xs text-gray-500 dark:text-gray-400">&gt;{config.dsat.target}% or &gt;11min</div>
                                        </div>
                                        <span className="text-xs text-gray-500 dark:text-gray-400 ml-2 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 px-2 py-1 rounded-full">
                                            {performanceStats.poor}
                                        </span>
                                    </button>
                                </div>
                            </div>
                        </div>

                        {/* Individual Agents Section - Right Panel */}
                        <div className="flex-1 flex flex-col">
                            <div className="p-3 border-b border-gray-200 dark:border-gray-600">
                                <div className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">INDIVIDUAL AGENTS</div>
                                <input
                                    type="text"
                                    placeholder="Search agents..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                />
                            </div>
                            <div className="flex-1 overflow-y-auto p-2">
                                <div className="grid grid-cols-1 gap-1">
                                    {filteredAgents.map(agent => (
                                        <label
                                            key={agent}
                                            className="flex items-center p-2 hover:bg-gray-50 dark:hover:bg-gray-600 cursor-pointer transition-colors rounded"
                                        >
                                            <input
                                                type="checkbox"
                                                checked={selectedAgents.includes(agent)}
                                                onChange={() => handleToggleAgent(agent)}
                                                className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
                                            />
                                            <span className="text-sm text-gray-900 dark:text-gray-100 truncate flex-1">
                                                {agent}
                                            </span>
                                            {selectedAgents.includes(agent) && (
                                                <svg className="w-4 h-4 text-blue-500 ml-2" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                                </svg>
                                            )}
                                        </label>
                                    ))}
                                    {filteredAgents.length === 0 && (
                                        <div className="p-4 text-center text-gray-500 dark:text-gray-400 text-sm">
                                            No agents found
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

function getPerformanceColor(score: number | null, excellent: number, good: number, target: number, type: "dsat" | "aht"): string {
    if (score === null) return "text-gray-400"; // Gray for missing data

    if (type === "dsat") {
        // Department-specific tier-based color system (lower DSAT is better)
        if (score <= excellent) return "text-green-600 dark:text-green-400"; // Tier 1 - Green
        if (score <= good) return "text-yellow-600 dark:text-yellow-400"; // Tier 2 - Yellow
        if (score <= target) return "text-orange-600 dark:text-orange-400"; // Tier 3 - Orange
        return "text-red-600 dark:text-red-400"; // Above Tier 3 - Red
    } else {
        // AHT uses standard tiers (9, 10, 11 minutes) - same for both departments
        if (score <= 9) return "text-green-600 dark:text-green-400"; // Tier 1 - Green
        if (score <= 10) return "text-yellow-600 dark:text-yellow-400"; // Tier 2 - Yellow
        if (score <= 11) return "text-orange-600 dark:text-orange-400"; // Tier 3 - Orange
        return "text-red-600 dark:text-red-400"; // Above Tier 3 - Red
    }
}

function getPerformanceBg(score: number | null, excellent: number, good: number, target: number, type: "dsat" | "aht"): string {
    if (score === null) return "bg-gray-50 dark:bg-gray-800"; // Gray background for missing data

    if (type === "dsat") {
        // Department-specific tier-based color system (lower DSAT is better)
        if (score <= excellent) return "bg-green-50 dark:bg-green-900/20"; // Tier 1 - Green
        if (score <= good) return "bg-yellow-50 dark:bg-yellow-900/20"; // Tier 2 - Yellow
        if (score <= target) return "bg-orange-50 dark:bg-orange-900/20"; // Tier 3 - Orange
        return "bg-red-50 dark:bg-red-900/20"; // Above Tier 3 - Red
    } else {
        // AHT uses standard tiers (9, 10, 11 minutes) - same for both departments
        if (score <= 9) return "bg-green-50 dark:bg-green-900/20"; // Tier 1 - Green
        if (score <= 10) return "bg-yellow-50 dark:bg-yellow-900/20"; // Tier 2 - Yellow
        if (score <= 11) return "bg-orange-50 dark:bg-orange-900/20"; // Tier 3 - Orange
        return "bg-red-50 dark:bg-red-900/20"; // Above Tier 3 - Red
    }
}

function getRankingIcon(rank: number): string {
    if (rank === 1) return "🥇";
    if (rank === 2) return "🥈";
    if (rank === 3) return "🥉";
    return `#${rank}`;
}

export const MTDRankingTable: React.FC<MTDRankingTableProps> = ({ data, mtdTeamPerformance }) => {
    const { config } = useDepartment();

    const [sortBy, setSortBy] = useState<"name" | "dsat" | "aht" | "qa" | "combined" | null>(null);
    const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
    const [filterPerformance, setFilterPerformance] = useState<"all" | "excellent" | "good" | "warning" | "poor">("all");
    const [selectedAgents, setSelectedAgents] = useState<string[]>([]);
    const [agentsInitialized, setAgentsInitialized] = useState(false);
    const [showPoster, setShowPoster] = useState(false);

    // Initialize selectedAgents with all agents when data changes
    useEffect(() => {
        if (data.length > 0 && !agentsInitialized) {
            setSelectedAgents(data.map(agent => agent.name));
            setAgentsInitialized(true);
        }
    }, [data, agentsInitialized]);

    // Calculate ranking-based data for display
    const rankedData = useMemo(() => {
        return data.map((agent, index) => {
            // RANKING ONLY USES DSAT AND AHT (QA excluded per user requirements)

            // For DSAT: already 0-100, lower is better
            const dsatScore = agent.dsat || 50; // Default to mid-range if missing

            // For AHT: normalize based on tier system
            // Scale: 9 min (Tier 1) = 0 points (perfect), 22 min (double max) = 100 points (max penalty)
            const ahtValue = agent.aht || 22; // Default to high penalty if missing
            const ahtScore = Math.min(((ahtValue - 9) / 9) * 100, 100);
            const ahtScoreNormalized = Math.max(0, ahtScore); // Ensure no negative scores

            // Weighted combined score (50% DSAT weight, 50% AHT weight)
            // Lower scores are better for ranking
            const combinedScore = (dsatScore * 0.5) + (ahtScoreNormalized * 0.5);

            return {
                ...agent,
                rank: index + 1,
                combinedScore
            };
        }).sort((a, b) => {
            // Sort by combined score (lower is better)
            return a.combinedScore - b.combinedScore;
        }).map((agent, index) => ({
            ...agent,
            rank: index + 1
        }));
    }, [data]);

    // Filter data based on selected agents
    const filteredData = useMemo(() => {
        // Show only selected agents
        return rankedData.filter(agent => selectedAgents.includes(agent.name));
    }, [rankedData, selectedAgents]);

    // Handle search and sorting
    const filteredAndSortedData = useMemo(() => {
        // Apply sorting
        return filteredData.sort((a, b) => {
            let aValue: string | number;
            let bValue: string | number;

            switch (sortBy) {
                case "name":
                    aValue = a.name;
                    bValue = b.name;
                    break;
                case "dsat":
                    aValue = a.dsat || 0;
                    bValue = b.dsat || 0;
                    break;
                case "aht":
                    aValue = a.aht || 0;
                    bValue = b.aht || 0;
                    break;
                case "qa":
                    aValue = a.qa || 0;
                    bValue = b.qa || 0;
                    break;
                case "combined":
                    aValue = a.combinedScore;
                    bValue = b.combinedScore;
                    break;
                default:
                    return 0;
            }

            if (typeof aValue === "string" && typeof bValue === "string") {
                const comparison = aValue.localeCompare(bValue);
                return sortOrder === "asc" ? comparison : -comparison;
            }

            const numA = typeof aValue === "number" ? aValue : 0;
            const numB = typeof bValue === "number" ? bValue : 0;
            const comparison = numA - numB;
            return sortOrder === "asc" ? comparison : -comparison;
        });
    }, [filteredData, sortBy, sortOrder]);

    const handleSort = (column: "name" | "dsat" | "aht" | "qa" | "combined") => {
        if (sortBy === column) {
            setSortOrder(sortOrder === "asc" ? "desc" : "asc");
        } else {
            setSortBy(column);
            setSortOrder("asc");
        }
    };

    const performanceStats = useMemo(() => {
        // Always use original data to show total tier counts regardless of filtering
        const excellent = data.filter(a => a.dsat <= config.dsat.excellent && (a.aht || 0) <= 9).length; // Tier 1 performance
        const good = data.filter(a =>
            (a.dsat <= config.dsat.good && (a.aht || 0) <= 10) &&
            !(a.dsat <= config.dsat.excellent && (a.aht || 0) <= 9)
        ).length; // Tier 2 performance
        const warning = data.filter(a =>
            (a.dsat <= config.dsat.target && (a.aht || 0) <= 11) &&
            !(a.dsat <= config.dsat.good && (a.aht || 0) <= 10)
        ).length; // Tier 3 performance
        const poor = data.length - excellent - good - warning; // Above Tier 3 performance

        return { excellent, good, warning, poor };
    }, [data, config.dsat.excellent, config.dsat.good, config.dsat.target]);

    // Team Performance: Use imported data (MTD BI Data) or calculate (APM Data)
    const teamPerformanceData = useMemo(() => {
        // Determine if any filters are active (agent selection or performance filtering)
        const hasAgentFiltering = selectedAgents.length !== data.length;
        const hasPerformanceFiltering = filterPerformance !== "all";
        const hasAnyFiltering = hasAgentFiltering || hasPerformanceFiltering;

        // Use filtered data when any filtering is active, otherwise use original data
        const teamData = hasAnyFiltering ? filteredData : data;

        if (teamData.length === 0) return { dsat: 0, aht: 0, qa: 0, isWeighted: false, isImported: false };

        // Check if we have imported MTD team performance data (from MTD BI Data)
        if (mtdTeamPerformance && !hasAnyFiltering) {
            // Always use imported team performance for MTD BI Data when no filtering is active
            // For MTD BI Data imports, use imported DSAT and AHT but calculate QA average from all data
            const validQA = data.filter(agent => agent.qa != null).map(agent => agent.qa!);
            const calculatedQA = validQA.length > 0
                ? validQA.reduce((sum, qa) => sum + qa, 0) / validQA.length
                : 0;

            return {
                dsat: mtdTeamPerformance.dsat,
                aht: mtdTeamPerformance.aht,
                qa: mtdTeamPerformance.qa || calculatedQA,
                isWeighted: false,
                isImported: true
            };
        }

        // Calculate team performance from filtered agents (APM Data or when filtering is active)
        // Calculate team DSAT (survey-weighted if survey data available)
        let teamDSAT = 0;
        const hasSurveyData = teamData.some(agent => agent.surveys && agent.surveys > 0);

        if (hasSurveyData) {
            // Calculate survey-weighted team DSAT
            let totalWeightedScore = 0;
            let totalSurveys = 0;

            const agentsWithSurveys = teamData.filter(agent =>
                agent.dsat != null && agent.surveys && agent.surveys > 0
            );

            agentsWithSurveys.forEach(agent => {
                if (agent.dsat !== null && agent.surveys) {
                    totalWeightedScore += agent.dsat * agent.surveys;
                    totalSurveys += agent.surveys;
                }
            });

            teamDSAT = totalSurveys > 0 ? totalWeightedScore / totalSurveys : 0;
        } else {
            // Fallback to simple average
            const validDSAT = teamData.filter(agent => agent.dsat != null).map(agent => agent.dsat!);
            teamDSAT = validDSAT.length > 0
                ? validDSAT.reduce((sum, dsat) => sum + dsat, 0) / validDSAT.length
                : 0;
        }

        // Calculate team AHT (simple average like QA)
        const validAHT = teamData.filter(agent => agent.aht != null).map(agent => agent.aht!);
        const teamAHT = validAHT.length > 0
            ? validAHT.reduce((sum, aht) => sum + aht, 0) / validAHT.length
            : 0;

        // Calculate team QA (simple average)
        const validQA = teamData.filter(agent => agent.qa != null).map(agent => agent.qa!);
        const teamQA = validQA.length > 0
            ? validQA.reduce((sum, qa) => sum + qa, 0) / validQA.length
            : 0;

        return {
            dsat: teamDSAT,
            aht: teamAHT,
            qa: teamQA,
            isWeighted: hasSurveyData,
            isImported: mtdTeamPerformance && !hasAnyFiltering
        };
    }, [filteredData, data, mtdTeamPerformance, selectedAgents, filterPerformance]);

    // Supervisor bonus calculation based on department configuration
    const supervisorBonus = useMemo(() => {
        const { dsat, aht, qa } = teamPerformanceData;

        // Use new scorecard system if available
        let baseBonus = 0;
        if (config.supervisorBonus.scorecardPayout && config.supervisorBonus.scorecardPayout.length > 0) {
            // Check eligibility requirements first
            if (dsat <= config.dsat.target && aht <= 10.83) {
                // Calculate scorecard percentage based on agents meeting targets
                const totalAgents = filteredData.length;
                if (totalAgents > 0) {
                    // Count agents meeting DSAT target
                    const dsatTargetAgents = filteredData.filter(agent =>
                        agent.dsat !== null && agent.dsat <= config.dsat.target
                    ).length;

                    // Count agents meeting AHT target
                    const ahtTargetAgents = filteredData.filter(agent =>
                        agent.aht !== null && agent.aht <= config.aht.target
                    ).length;

                    // Count agents meeting QA target (≥ 85% for supervisors)
                    const qaTargetAgents = filteredData.filter(agent =>
                        agent.qa !== null && agent.qa >= 85
                    ).length;

                    // Calculate weighted scorecard percentage
                    const dsatPercentage = (dsatTargetAgents / totalAgents) * 100;
                    const ahtPercentage = (ahtTargetAgents / totalAgents) * 100;
                    const qaPercentage = (qaTargetAgents / totalAgents) * 100;

                    const scorecardScore =
                        (dsatPercentage * config.supervisorBonus.scorecardWeights.dsat.weight / 100) +
                        (ahtPercentage * config.supervisorBonus.scorecardWeights.aht.weight / 100) +
                        (qaPercentage * config.supervisorBonus.scorecardWeights.vpi.weight / 100);

                    // Find matching payout tier
                    for (const tier of config.supervisorBonus.scorecardPayout) {
                        if (scorecardScore >= tier.minScore && scorecardScore <= tier.maxScore) {
                            baseBonus = tier.amount;
                            break;
                        }
                    }
                }
            }
        } else {
            // Fallback to legacy tier system
            // Supervisor bonus requires ALL metrics like agent bonuses
            // Check minimum QA requirement first
            if (qa < config.qa.target) {
                return 0; // No bonus if team QA below minimum requirement
            }

            // Check each tier in order (tier1 = best performance, highest bonus)
            for (const tier of config.supervisorBonus.tier1) {
                if (dsat <= tier.dsat && aht <= tier.aht) {
                    baseBonus = tier.amount;
                    break;
                }
            }

            if (baseBonus === 0) {
                for (const tier of config.supervisorBonus.tier2) {
                    if (dsat <= tier.dsat && aht <= tier.aht) {
                        baseBonus = tier.amount;
                        break;
                    }
                }
            }

            if (baseBonus === 0) {
                for (const tier of config.supervisorBonus.tier3) {
                    if (dsat <= tier.dsat && aht <= tier.aht) {
                        baseBonus = tier.amount;
                        break;
                    }
                }
            }
        }

        // Calculate team recognition bonus for supervisors
        let recognitionBonus = 0;
        // Check if team meets the minimum requirements for team recognition
        if (qa >= 80 && aht <= 11) {
            // Check each tier in order (best to worst)
            for (const goal of config.teamRecognition.supervisorGoals) {
                if (dsat <= goal.dsat) {
                    recognitionBonus = goal.amount;
                    break;
                }
            }
        }

        return baseBonus + recognitionBonus;
    }, [teamPerformanceData, config, filteredData]);

    return (
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700">
            {/* Header */}
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <div className="flex flex-col gap-4">
                    <div>
                        <h3 className="text-2xl font-bold text-gray-800 dark:text-gray-200">
                            MTD Performance Ranking
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                            {config.name} • DSAT Target: ≤{config.dsat.excellent}% • AHT Target: ≤{config.aht.excellent} min • {filteredAndSortedData.length} agents • TP row reflects all active filters
                        </p>
                    </div>

                    {/* Performance Stats Row */}
                    <div className="grid grid-cols-5 gap-2 text-center">
                        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-2 border border-gray-200 dark:border-gray-700">
                            <div className="text-lg font-bold text-gray-700 dark:text-gray-200">{data.length}</div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">Total</div>
                        </div>
                        <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-2 border border-green-200 dark:border-green-700">
                            <div className="text-lg font-bold text-green-600 dark:text-green-400">{performanceStats.excellent}</div>
                            <div className="text-xs text-green-600 dark:text-green-400">Tier 1</div>
                            <div className="text-xs text-green-500 dark:text-green-400 opacity-75">≤{config.dsat.excellent}% • ≤9min</div>
                        </div>
                        <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-2 border border-yellow-200 dark:border-yellow-700">
                            <div className="text-lg font-bold text-yellow-600 dark:text-yellow-400">{performanceStats.good}</div>
                            <div className="text-xs text-yellow-600 dark:text-yellow-400">Tier 2</div>
                            <div className="text-xs text-yellow-500 dark:text-yellow-400 opacity-75">≤{config.dsat.good}% • ≤10min</div>
                        </div>
                        <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-2 border border-orange-200 dark:border-orange-700">
                            <div className="text-lg font-bold text-orange-600 dark:text-orange-400">{performanceStats.warning}</div>
                            <div className="text-xs text-orange-600 dark:text-orange-400">Tier 3</div>
                            <div className="text-xs text-orange-500 dark:text-orange-400 opacity-75">≤{config.dsat.target}% • ≤11min</div>
                        </div>
                        <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-2 border border-red-200 dark:border-red-700">
                            <div className="text-lg font-bold text-red-600 dark:text-red-400">{performanceStats.poor}</div>
                            <div className="text-xs text-red-600 dark:text-red-400">Above T3</div>
                            <div className="text-xs text-red-500 dark:text-red-400 opacity-75">&gt;{config.dsat.target}% or &gt;11min</div>
                        </div>
                    </div>

                    {/* Controls Row */}
                    <div className="flex items-center justify-between gap-3">
                        <div className="flex items-center gap-2">
                            <AgentMultiSelect
                                agents={data.map(agent => agent.name)}
                                selectedAgents={selectedAgents}
                                onSelectionChange={setSelectedAgents}
                                performanceFilter={filterPerformance}
                                onPerformanceFilterChange={setFilterPerformance}
                                performanceStats={performanceStats}
                                config={config}
                                data={data}
                            />
                        </div>

                        <div className="flex items-center gap-2">
                            <button
                                onClick={() => setShowPoster(true)}
                                className="inline-flex items-center gap-1.5 px-3 py-1.5 bg-purple-600 hover:bg-purple-700 text-white rounded-md font-medium transition-colors duration-200 text-sm"
                            >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                                Poster
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            {/* Table */}
            <div className="overflow-x-auto">
                <table className="w-full">
                    <thead className="bg-gray-50 dark:bg-gray-900">
                        <tr>
                            <th className="px-6 py-4 text-left text-xs font-bold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                                Rank
                            </th>
                            <th
                                className="px-6 py-4 text-left text-xs font-bold text-gray-600 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800"
                                onClick={() => handleSort("name")}
                            >
                                <div className="flex items-center gap-2">
                                    Agent Name
                                    {sortBy === "name" && (
                                        <span className="text-blue-500">
                                            {sortOrder === "asc" ? "↑" : "↓"}
                                        </span>
                                    )}
                                </div>
                            </th>
                            <th
                                className="px-6 py-4 text-center text-xs font-bold text-gray-600 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800"
                                onClick={() => handleSort("dsat")}
                            >
                                <div className="flex items-center justify-center gap-2">
                                    MTD DSAT
                                    {sortBy === "dsat" && (
                                        <span className="text-blue-500">
                                            {sortOrder === "asc" ? "↑" : "↓"}
                                        </span>
                                    )}
                                </div>
                            </th>
                            <th
                                className="px-6 py-4 text-center text-xs font-bold text-gray-600 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800"
                                onClick={() => handleSort("aht")}
                            >
                                <div className="flex items-center justify-center gap-2">
                                    AHT (mm:ss)
                                    {sortBy === "aht" && (
                                        <span className="text-blue-500">
                                            {sortOrder === "asc" ? "↑" : "↓"}
                                        </span>
                                    )}
                                </div>
                            </th>
                            <th
                                className="px-6 py-4 text-center text-xs font-bold text-gray-600 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800"
                                onClick={() => handleSort("qa")}
                            >
                                <div className="flex items-center justify-center gap-2">
                                    QA Score (%)
                                    {sortBy === "qa" && (
                                        <span className="text-blue-500">
                                            {sortOrder === "asc" ? "↑" : "↓"}
                                        </span>
                                    )}
                                </div>
                            </th>
                            {data.some(d => d.surveys !== undefined) && (
                                <th className="px-4 py-4 text-center text-xs font-bold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                                    <div className="text-center">
                                        <div>Total</div>
                                        <div>Surveys</div>
                                    </div>
                                </th>
                            )}
                            {data.some(d => d.surveys !== undefined) && (
                                <th className="px-4 py-4 text-center text-xs font-bold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                                    <div className="text-center">
                                        <div className="text-emerald-600 dark:text-emerald-400">Positive</div>
                                        <div className="text-emerald-600 dark:text-emerald-400">Surveys</div>
                                    </div>
                                </th>
                            )}
                            {data.some(d => d.surveys !== undefined) && (
                                <th className="px-4 py-4 text-center text-xs font-bold text-gray-600 dark:text-gray-300 uppercase tracking-wider">
                                    <div className="text-center">
                                        <div className="text-red-600 dark:text-red-400">Negative</div>
                                        <div className="text-red-600 dark:text-red-400">Surveys</div>
                                    </div>
                                </th>
                            )}
                            <th
                                className="px-6 py-4 text-center text-xs font-bold text-gray-600 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800"
                                onClick={() => handleSort("combined")}
                            >
                                <div className="flex items-center justify-center gap-2">
                                    Overall Score
                                    {sortBy === "combined" && (
                                        <span className="text-blue-500">
                                            {sortOrder === "asc" ? "↑" : "↓"}
                                        </span>
                                    )}
                                </div>
                            </th>
                        </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                        {/* Team Performance Section */}
                        <tr className="bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 border-l-4 border-purple-500">
                            <td className="px-6 py-4 whitespace-nowrap text-center">
                                <div className="text-2xl">
                                    👑
                                </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap min-w-[200px]">
                                <div className="flex items-center">
                                    <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center text-white font-bold text-sm mr-3 flex-shrink-0">
                                        TP
                                    </div>
                                    <div className="font-bold text-purple-800 dark:text-purple-300 flex-1 min-w-0">
                                        <div className="truncate">
                                            Team Performance
                                        </div>
                                        <div className="text-xs font-normal text-purple-600 dark:text-purple-400">
                                            {teamPerformanceData.isImported
                                                ? "📊 MTD BI Data"
                                                : teamPerformanceData.isWeighted
                                                    ? "📝 Survey-weighted"
                                                    : "📈 Calculated average"
                                            }
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td className={clsx(
                                "px-6 py-4 whitespace-nowrap text-center",
                                getPerformanceBg(teamPerformanceData.dsat, config.dsat.excellent, config.dsat.good, config.dsat.target, "dsat")
                            )}>
                                <div className={clsx(
                                    "text-lg font-bold",
                                    getPerformanceColor(teamPerformanceData.dsat, config.dsat.excellent, config.dsat.good, config.dsat.target, "dsat")
                                )}>
                                    {teamPerformanceData.dsat.toFixed(2)}%
                                </div>
                            </td>
                            <td className={clsx(
                                "px-6 py-4 whitespace-nowrap text-center",
                                getPerformanceBg(teamPerformanceData.aht, 9, 10, 11, "aht")
                            )}>
                                <div className={clsx(
                                    "text-lg font-bold",
                                    getPerformanceColor(teamPerformanceData.aht, 9, 10, 11, "aht")
                                )}>
                                    {teamPerformanceData.aht != null ? convertDecimalMinutesToTime(teamPerformanceData.aht) : '-'}
                                </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-center">
                                <div className={clsx(
                                    "text-lg font-bold",
                                    teamPerformanceData.qa != null
                                        ? teamPerformanceData.qa >= config.qa.excellent
                                            ? "text-green-600 dark:text-green-400"
                                            : teamPerformanceData.qa >= config.qa.target
                                                ? "text-yellow-600 dark:text-yellow-400"
                                                : "text-red-600 dark:text-red-400"
                                        : "text-gray-400"
                                )}>
                                    {teamPerformanceData.qa != null ? teamPerformanceData.qa.toFixed(2) + "%" : '-'}
                                </div>
                            </td>
                            {data.some(d => d.surveys !== undefined) && (
                                <>
                                    <td className="px-4 py-4 whitespace-nowrap text-center">
                                        <div className="text-sm font-medium text-gray-600 dark:text-gray-400">
                                            {mtdTeamPerformance
                                                ? data.reduce((sum, d) => sum + (d.surveys || 0), 0)
                                                : (filteredData.length > 0 ? filteredData : data).reduce((sum, d) => sum + (d.surveys || 0), 0)
                                            }
                                        </div>
                                    </td>
                                    <td className="px-4 py-4 whitespace-nowrap text-center">
                                        <div className="text-sm font-medium text-emerald-600 dark:text-emerald-400">
                                            {mtdTeamPerformance
                                                ? data.reduce((sum, d) => sum + (d.positiveSurveys || 0), 0)
                                                : (filteredData.length > 0 ? filteredData : data).reduce((sum, d) => sum + (d.positiveSurveys || 0), 0)
                                            }
                                        </div>
                                    </td>
                                    <td className="px-4 py-4 whitespace-nowrap text-center">
                                        <div className="text-sm font-medium text-red-600 dark:text-red-400">
                                            {mtdTeamPerformance
                                                ? data.reduce((sum, d) => sum + (d.negativeSurveys || 0), 0)
                                                : (filteredData.length > 0 ? filteredData : data).reduce((sum, d) => sum + (d.negativeSurveys || 0), 0)
                                            }
                                        </div>
                                    </td>
                                </>
                            )}
                            <td className="px-6 py-4 whitespace-nowrap text-center">
                                <div className="text-sm font-medium text-purple-600 dark:text-purple-400">
                                    <div className="font-bold text-lg">
                                        {supervisorBonus > 0 ? `$${(supervisorBonus / 1000).toFixed(0)}K` : 'No Bonus'}
                                    </div>
                                    <div className="text-xs">
                                        Supervisor Bonus
                                    </div>
                                    {teamPerformanceData.qa >= 80 && teamPerformanceData.aht <= 11 && teamPerformanceData.dsat <= config.teamRecognition.supervisorGoals[0].dsat && (
                                        <div className="text-xs text-amber-600 dark:text-amber-400 mt-1">
                                            Includes Team Recognition
                                        </div>
                                    )}
                                </div>
                            </td>
                        </tr>

                        {filteredAndSortedData.map((agent) => (
                            <tr
                                key={agent.name}
                                className="hover:bg-gray-50 dark:hover:bg-gray-900/50 transition-colors"
                            >
                                <td className="px-6 py-4 whitespace-nowrap text-center">
                                    <div className="text-2xl">
                                        {getRankingIcon(agent.rank)}
                                    </div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap min-w-[200px]">
                                    <div className="flex items-center">
                                        <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center text-purple-600 dark:text-purple-300 font-bold text-sm mr-3 flex-shrink-0">
                                            {agent.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                                        </div>
                                        <div className="font-medium text-gray-900 dark:text-gray-100 flex-1 min-w-0">
                                            <div className="truncate" title={agent.name}>
                                                {agent.name}
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td className={clsx(
                                    "px-6 py-4 whitespace-nowrap text-center",
                                    getPerformanceBg(agent.dsat, config.dsat.excellent, config.dsat.good, config.dsat.target, "dsat")
                                )}>
                                    <div className={clsx(
                                        "text-lg font-bold",
                                        getPerformanceColor(agent.dsat, config.dsat.excellent, config.dsat.good, config.dsat.target, "dsat")
                                    )}>
                                        {agent.dsat != null ? agent.dsat.toFixed(2) + "%" : "-"}
                                    </div>
                                </td>
                                <td className={clsx(
                                    "px-6 py-4 whitespace-nowrap text-center",
                                    getPerformanceBg(agent.aht, 9, 10, 11, "aht")
                                )}>
                                    <div className={clsx(
                                        "text-lg font-bold",
                                        getPerformanceColor(agent.aht, 9, 10, 11, "aht")
                                    )}>
                                        {agent.aht != null ? convertDecimalMinutesToTime(agent.aht) : '-'}
                                    </div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-center">
                                    <div className={clsx(
                                        "text-lg font-bold",
                                        agent.qa != null
                                            ? agent.qa >= config.qa.excellent
                                                ? "text-green-600 dark:text-green-400"
                                                : agent.qa >= config.qa.target
                                                    ? "text-yellow-600 dark:text-yellow-400"
                                                    : "text-red-600 dark:text-red-400"
                                            : "text-gray-400"
                                    )}>
                                        {agent.qa != null ? agent.qa.toFixed(2) + "%" : '-'}
                                    </div>
                                </td>
                                {data.some(d => d.surveys !== undefined) && (
                                    <td className="px-4 py-4 whitespace-nowrap text-center">
                                        <div className="text-sm font-medium text-gray-600 dark:text-gray-400">
                                            {agent.surveys || 0}
                                        </div>
                                    </td>
                                )}
                                {data.some(d => d.surveys !== undefined) && (
                                    <td className="px-4 py-4 whitespace-nowrap text-center">
                                        <div className="text-sm font-medium text-emerald-600 dark:text-emerald-400">
                                            {agent.positiveSurveys || 0}
                                        </div>
                                    </td>
                                )}
                                {data.some(d => d.surveys !== undefined) && (
                                    <td className="px-4 py-4 whitespace-nowrap text-center">
                                        <div className="text-sm font-medium text-red-600 dark:text-red-400">
                                            {agent.negativeSurveys || 0}
                                        </div>
                                    </td>
                                )}
                                <td className="px-6 py-4 whitespace-nowrap text-center">
                                    <div className="text-sm font-medium text-gray-600 dark:text-gray-400">
                                        {agent.combinedScore.toFixed(2)}
                                    </div>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>

            {filteredAndSortedData.length === 0 && (
                <div className="p-8 text-center text-gray-500 dark:text-gray-400">
                    No agents found matching your criteria.
                </div>
            )}

            {/* Poster Modal */}
            {showPoster && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                    <div className="bg-white dark:bg-gray-900 rounded-2xl shadow-2xl max-w-full max-h-full overflow-auto">
                        <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                            <h2 className="text-xl font-bold text-gray-800 dark:text-gray-200">
                                MTD Ranking Poster
                            </h2>
                            <button
                                onClick={() => setShowPoster(false)}
                                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                            >
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                        <div className="p-4">
                            <MTDRankingPoster
                                data={data}
                                filteredData={filteredAndSortedData}
                                filterPerformance={filterPerformance !== "all" ? filterPerformance : undefined}
                                selectedAgent={undefined}
                                filteredAndSortedData={filteredAndSortedData}
                                teamPerformanceData={teamPerformanceData}
                                performanceStats={performanceStats}
                            />
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}; 