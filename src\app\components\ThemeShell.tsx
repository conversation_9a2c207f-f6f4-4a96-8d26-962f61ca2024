"use client";
import { ThemeProvider } from "next-themes";
import { useEffect, useState } from "react";

interface ThemeShellProps {
    children: React.ReactNode;
}

export default function ThemeShell({ children }: ThemeShellProps) {
    // Prevent hydration mismatch
    const [mounted, setMounted] = useState(false);
    useEffect(() => setMounted(true), []);

    if (!mounted) {
        // Return children without theme provider during SSR
        return <>{children}</>;
    }

    return (
        <ThemeProvider attribute="class" defaultTheme="dark" forcedTheme="dark">
            {children}
        </ThemeProvider>
    );
}
