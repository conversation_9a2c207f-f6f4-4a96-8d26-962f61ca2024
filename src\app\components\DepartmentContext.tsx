"use client";
import React, { createContext, useContext, useState } from 'react';

export type Department = 'customer-support' | 'inbound-concierge';

export interface DepartmentTargets {
    name: string;
    dsat: {
        excellent: number; // ≤ this value
        good: number; // ≤ this value  
        target: number; // ≤ this value
    };
    aht: {
        excellent: number; // ≤ this value (in minutes)
        good: number; // ≤ this value (in minutes)
        target: number; // ≤ this value (in minutes)
    };
    qa: {
        excellent: number; // ≥ this value (percentage)
        good: number; // ≥ this value (percentage)
        target: number; // ≥ this value (percentage, minimum for bonus eligibility)
    };
    agentBonus: {
        tier1: { dsat: number; aht: number; amount: number }[]; // 38% or less
        tier2: { dsat: number; aht: number; amount: number }[]; // 39.99% - 38.01%
        tier3: { dsat: number; aht: number; amount: number }[]; // 42% - 40%
    };
    supervisorBonus: {
        eligibilityRequirements: string[];
        scorecardWeights: {
            dsat: { weight: number; formula: string };
            aht: { weight: number; formula: string };
            vpi: { weight: number; formula: string };
        };
        scorecardPayout: {
            range: string;
            minScore: number;
            maxScore: number;
            amount: number;
        }[];
        // Legacy tier system (kept for backward compatibility)
        tier1: { dsat: number; aht: number; amount: number }[];
        tier2: { dsat: number; aht: number; amount: number }[];
        tier3: { dsat: number; aht: number; amount: number }[];
    };
    teamRecognition: {
        agentGoal: { dsat: number; amount: number };
        supervisorGoals: { dsat: number; amount: number }[];
    };
}

const departmentConfigs: Record<Department, DepartmentTargets> = {
    'customer-support': {
        name: 'Customer Support',
        dsat: {
            excellent: 37.40, // Tier 1: ≤37.40%
            good: 40.99,      // Tier 2: 37.41% - 40.99%
            target: 44.00     // Tier 3: 41.00% - 44.00% (above this is red)
        },
        aht: {
            excellent: 9,    // Excellent AHT (≤9 min)
            good: 9.83,     // Good AHT (≤9:50 min)
            target: 10.83   // General AHT target (≤10:50 min, max bonus eligibility)
        },
        qa: {
            excellent: 90,   // Excellent QA (≥90%)
            good: 85,       // Good QA (≥85%)
            target: 75      // Minimum QA target (≥75%, required for any bonus eligibility)
        },
        agentBonus: {
            tier1: [ // 37.4% or less
                { dsat: 37.4, aht: 9, amount: 700000 },
                { dsat: 37.4, aht: 9.83, amount: 650000 },
                { dsat: 37.4, aht: 10.83, amount: 600000 }
            ],
            tier2: [ // 40.99% - 37.41%
                { dsat: 40.99, aht: 9, amount: 550000 },
                { dsat: 40.99, aht: 9.83, amount: 500000 },
                { dsat: 40.99, aht: 10.83, amount: 450000 }
            ],
            tier3: [ // 44.00% - 41.00%
                { dsat: 44.00, aht: 9, amount: 400000 },
                { dsat: 44.00, aht: 9.83, amount: 350000 },
                { dsat: 44.00, aht: 10.83, amount: 300000 }
            ]
        },
        supervisorBonus: {
            eligibilityRequirements: [
                "No written warnings/Suspensions",
                "No unjustified absences",
                "Monthly DSAT Granted excluded on minimum target (Bottom team: Below 44%)",
                "Monthly AHT of 0:10:50 or less",
                "Verbal warning removes 50% of the bonus",
                "Payout proportional to attendance (Attendance 51% or above)"
            ],
            scorecardWeights: {
                dsat: { weight: 50, formula: "Agents on Target / Total Agents" },
                aht: { weight: 30, formula: "Agents on Target / Total Agents" },
                vpi: { weight: 20, formula: "Agents on Target / Total Agents" }
            },
            scorecardPayout: [
                { range: "55%-59.99%", minScore: 55, maxScore: 59.99, amount: 100000 },
                { range: "60%-70%", minScore: 60, maxScore: 70, amount: 250000 },
                { range: "70.01%-80%", minScore: 70.01, maxScore: 80, amount: 325000 },
                { range: "80.01%-84.99%", minScore: 80.01, maxScore: 84.99, amount: 400000 },
                { range: "85%-90%", minScore: 85, maxScore: 90, amount: 500000 },
                { range: "90.01%-100%", minScore: 90.01, maxScore: 100, amount: 600000 }
            ],
            // Legacy tier system (kept for backward compatibility)
            tier1: [ // 37.4% or less
                { dsat: 37.4, aht: 9, amount: 500000 },
                { dsat: 37.4, aht: 9.83, amount: 425000 },
                { dsat: 37.4, aht: 10.83, amount: 400000 }
            ],
            tier2: [ // 40.99% - 37.41%
                { dsat: 40.99, aht: 9, amount: 350000 },
                { dsat: 40.99, aht: 9.83, amount: 325000 },
                { dsat: 40.99, aht: 10.83, amount: 300000 }
            ],
            tier3: [ // 44.00% - 41.00%
                { dsat: 44.00, aht: 9, amount: 250000 },
                { dsat: 44.00, aht: 9.83, amount: 225000 },
                { dsat: 44.00, aht: 10.83, amount: 200000 }
            ]
        },
        teamRecognition: {
            agentGoal: { dsat: 42, amount: 150000 },
            supervisorGoals: [
                { dsat: 42, amount: 300000 },
                { dsat: 40, amount: 400000 },
                { dsat: 37, amount: 500000 }
            ]
        }
    },
    'inbound-concierge': {
        name: 'Inbound Concierge',
        dsat: {
            excellent: 34.85, // Tier 1: ≤34.85%
            good: 37.99,      // Tier 2: 34.86% - 37.99%  
            target: 41.00     // Tier 3: 38.00% - 41.00% (above this is red)
        },
        aht: {
            excellent: 9,     // Excellent AHT (≤9 min)
            good: 9.83,       // Good AHT (≤9:50 min)
            target: 10.83     // General AHT target (≤10:50 min, max bonus eligibility)
        },
        qa: {
            excellent: 90,   // Excellent QA (≥90%)
            good: 85,       // Good QA (≥85%)
            target: 70      // Minimum QA target (≥70%, required for any bonus eligibility)
        },
        agentBonus: {
            tier1: [ // 34.85% or less
                { dsat: 34.85, aht: 9, amount: 700000 },
                { dsat: 34.85, aht: 9.83, amount: 650000 },
                { dsat: 34.85, aht: 10.83, amount: 600000 }
            ],
            tier2: [ // 37.99% - 34.84%
                { dsat: 37.99, aht: 9, amount: 550000 },
                { dsat: 37.99, aht: 9.83, amount: 500000 },
                { dsat: 37.99, aht: 10.83, amount: 450000 }
            ],
            tier3: [ // 41% - 38%
                { dsat: 41, aht: 9, amount: 400000 },
                { dsat: 41, aht: 9.83, amount: 350000 },
                { dsat: 41, aht: 10.83, amount: 300000 }
            ]
        },
        supervisorBonus: {
            eligibilityRequirements: [
                "No written warnings/Suspensions",
                "No unjustified absences",
                "Monthly DSAT Granted excluded on minimum target (Bottom team: Below 41%)",
                "Monthly AHT of 0:10:50 or less",
                "Verbal warning removes 50% of the bonus",
                "Payout proportional to attendance (Attendance 51% or above)"
            ],
            scorecardWeights: {
                dsat: { weight: 50, formula: "Agents on Target / Total Agents" },
                aht: { weight: 30, formula: "Agents on Target / Total Agents" },
                vpi: { weight: 20, formula: "Agents on Target / Total Agents" }
            },
            scorecardPayout: [
                { range: "55%-59.99%", minScore: 55, maxScore: 59.99, amount: 100000 },
                { range: "60%-70%", minScore: 60, maxScore: 70, amount: 250000 },
                { range: "70.01%-80%", minScore: 70.01, maxScore: 80, amount: 325000 },
                { range: "80.01%-84.99%", minScore: 80.01, maxScore: 84.99, amount: 400000 },
                { range: "85%-90%", minScore: 85, maxScore: 90, amount: 500000 },
                { range: "90.01%-100%", minScore: 90.01, maxScore: 100, amount: 600000 }
            ],
            // Legacy tier system (kept for backward compatibility)
            tier1: [ // 34.85% or less
                { dsat: 34.85, aht: 9, amount: 500000 },
                { dsat: 34.85, aht: 9.83, amount: 425000 },
                { dsat: 34.85, aht: 10.83, amount: 400000 }
            ],
            tier2: [ // 37.99% - 34.86%
                { dsat: 37.99, aht: 9, amount: 350000 },
                { dsat: 37.99, aht: 9.83, amount: 325000 },
                { dsat: 37.99, aht: 10.83, amount: 300000 }
            ],
            tier3: [ // 41% - 38%
                { dsat: 41, aht: 9, amount: 250000 },
                { dsat: 41, aht: 9.83, amount: 225000 },
                { dsat: 41, aht: 10.83, amount: 200000 }
            ]
        },
        teamRecognition: {
            agentGoal: { dsat: 38, amount: 150000 },
            supervisorGoals: [
                { dsat: 38, amount: 300000 },
                { dsat: 36, amount: 400000 },
                { dsat: 34, amount: 500000 }
            ]
        }
    }
};

interface DepartmentContextType {
    department: Department;
    setDepartment: (dept: Department) => void;
    config: DepartmentTargets;
}

const DepartmentContext = createContext<DepartmentContextType | undefined>(undefined);

export const DepartmentProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const [department, setDepartment] = useState<Department>('customer-support');

    const config = departmentConfigs[department];

    return (
        <DepartmentContext.Provider value={{ department, setDepartment, config }}>
            {children}
        </DepartmentContext.Provider>
    );
};

export const useDepartment = () => {
    const context = useContext(DepartmentContext);
    if (context === undefined) {
        throw new Error('useDepartment must be used within a DepartmentProvider');
    }
    return context;
};

export { departmentConfigs }; 