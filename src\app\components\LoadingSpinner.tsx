"use client";
import React from 'react';
import { Loader2 } from 'lucide-react';

interface LoadingSpinnerProps {
    size?: 'sm' | 'md' | 'lg';
    text?: string;
    className?: string;
}

export function LoadingSpinner({
    size = 'md',
    text = 'Loading...',
    className = ''
}: LoadingSpinnerProps) {
    const sizeClasses = {
        sm: 'w-4 h-4',
        md: 'w-6 h-6',
        lg: 'w-8 h-8'
    };

    const textSizeClasses = {
        sm: 'text-sm',
        md: 'text-base',
        lg: 'text-lg'
    };

    return (
        <div className={`flex items-center justify-center gap-3 ${className}`}>
            <Loader2 className={`${sizeClasses[size]} animate-spin text-blue-600 dark:text-blue-400`} />
            {text && (
                <span className={`${textSizeClasses[size]} text-gray-600 dark:text-gray-300`}>
                    {text}
                </span>
            )}
        </div>
    );
}

export function LoadingOverlay({
    isLoading,
    text = 'Processing...',
    children
}: {
    isLoading: boolean;
    text?: string;
    children: React.ReactNode;
}) {
    return (
        <div className="relative">
            {children}
            {isLoading && (
                <div className="absolute inset-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm flex items-center justify-center z-50 rounded-lg">
                    <LoadingSpinner size="lg" text={text} />
                </div>
            )}
        </div>
    );
} 