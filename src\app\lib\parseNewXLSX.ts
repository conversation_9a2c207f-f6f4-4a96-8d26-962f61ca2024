import * as XLSX from 'xlsx';
import { MTDRanking } from './parseKPI';

// Data structure for daily performance records
export interface DailyDSATRecord {
    date: string;
    agent: string;
    dsat: number | null; // null for blank/missing DSAT values
    surveys: number;
    positiveSurveys: number; // Calculated: surveys - negativeSurveys
    negativeSurveys: number; // Calculated: surveys * (dsat/100)
}

export interface ParsedDailyData {
    records: DailyDSATRecord[];
    dates: string[];
    agents: string[];
    mtdData: AgentMTDData[]; // MTD calculations per agent
    teamPerformanceData?: { [date: string]: number | null }; // Optional: Team Performance from Daily BI Data Total row
}

// New interface for MTD data per agent
export interface AgentMTDData {
    agent: string;
    totalSurveys: number;
    totalPositiveSurveys: number;
    totalNegativeSurveys: number;
    mtdDSAT: number | null; // Calculated: (totalNegativeSurveys / totalSurveys) * 100
    aht?: number | null; // Optional AHT data
    qa?: number | null; // Optional QA data (percentage 0-100)
}


/**
 * Parse the new XLSX format with columns:
 * - UTC Date: Month
 * - Agent  
 * - DSAT
 * - Number of Surveys responded
 */
export function parseNewXLSXFormat(buffer: ArrayBuffer): ParsedDailyData {
    const result: ParsedDailyData = {
        records: [],
        dates: [],
        agents: [],
        mtdData: []
    };

    try {
        // Load the workbook
        const workbook = XLSX.read(buffer, { type: 'array' });

        // Get first sheet
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];

        // Convert to JSON with header:1 to get array of arrays
        const data = XLSX.utils.sheet_to_json<unknown[]>(worksheet, { header: 1 });

        // Find the header row
        let headerRowIndex = -1;
        let dateColIndex = -1;
        let agentColIndex = -1;
        let dsatColIndex = -1;
        let surveysColIndex = -1;

        // Look for header row containing the expected column names
        for (let i = 0; i < Math.min(5, data.length); i++) {
            const row = data[i];
            if (!row || !Array.isArray(row) || row.length === 0) continue;

            // Find column indices
            for (let j = 0; j < row.length; j++) {
                const cell = row[j];
                if (!cell) continue;

                const cellStr = String(cell).toLowerCase();

                if (cellStr.includes('utc date') || cellStr.includes('date')) {
                    dateColIndex = j;
                } else if (cellStr.includes('agent')) {
                    agentColIndex = j;
                } else if (cellStr.includes('dsat')) {
                    dsatColIndex = j;
                } else if (cellStr.includes('survey') || cellStr.includes('number')) {
                    surveysColIndex = j;
                }
            }

            // If we found all required columns, this is our header row
            if (dateColIndex !== -1 && agentColIndex !== -1 && dsatColIndex !== -1 && surveysColIndex !== -1) {
                headerRowIndex = i;
                break;
            }
        }

        if (headerRowIndex === -1) {
            return result;
        }

        // Process data rows
        const uniqueDates = new Set<string>();
        const uniqueAgents = new Set<string>();

        for (let i = headerRowIndex + 1; i < data.length; i++) {
            const row = data[i];
            if (!row || !Array.isArray(row) || row.length === 0) continue;

            // Extract values from each column
            const dateCell = row[dateColIndex];
            const agentCell = row[agentColIndex];
            const dsatCell = row[dsatColIndex];
            const surveysCell = row[surveysColIndex];

            // Skip rows where essential data is missing
            if (!dateCell || !agentCell) {
                continue;
            }

            // Parse date
            const dateStr = parseDate(dateCell);
            if (!dateStr) {
                continue;
            }

            // Parse agent name
            const agentName = String(agentCell).trim();
            if (!agentName) {
                continue;
            }

            // Parse DSAT (can be null/blank)
            const dsatValue = parseDSAT(dsatCell);

            // Parse surveys count
            const surveysCount = parseSurveys(surveysCell);

            // Calculate positive and negative surveys
            const surveyBreakdown = calculateSurveyBreakdown(surveysCount, dsatValue);

            // Create record
            const record: DailyDSATRecord = {
                date: dateStr,
                agent: agentName,
                dsat: dsatValue,
                surveys: surveysCount,
                positiveSurveys: surveyBreakdown.positiveSurveys,
                negativeSurveys: surveyBreakdown.negativeSurveys
            };

            result.records.push(record);
            uniqueDates.add(dateStr);
            uniqueAgents.add(agentName);
        }

        // Sort dates and agents
        result.dates = Array.from(uniqueDates).sort((a, b) => new Date(a).getTime() - new Date(b).getTime());
        result.agents = Array.from(uniqueAgents).sort();

        // Calculate MTD data
        result.mtdData = calculateMTDData(result.records);

        return result;

    } catch (error) {
        console.error("Error parsing new XLSX format:", error);
        return result;
    }
}

/**
 * Parse date from various formats (e.g., "June 1, 2025", Excel date numbers)
 */
function parseDate(dateCell: unknown): string | null {
    if (!dateCell) return null;

    try {
        // Handle Date objects
        if (dateCell instanceof Date) {
            return formatDateToISO(dateCell);
        }

        // Handle Excel date numbers using XLSX's built-in conversion
        if (typeof dateCell === 'number' && dateCell > 40000 && dateCell < 50000) {
            // Use XLSX's built-in date conversion which handles Excel's quirks correctly
            const date = XLSX.SSF.parse_date_code(dateCell);
            if (date) {
                // Create a proper Date object from XLSX's parsed date
                const jsDate = new Date(date.y, date.m - 1, date.d); // month is 0-based in JS
                return formatDateToISO(jsDate);
            }

            // Fallback to manual calculation if XLSX method fails
            const excelEpoch = new Date(1899, 11, 30); // Dec 30, 1899
            const msPerDay = 24 * 60 * 60 * 1000;
            const jsDate = new Date(excelEpoch.getTime() + dateCell * msPerDay);
            return formatDateToISO(jsDate);
        }

        // Handle string dates
        if (typeof dateCell === 'string') {
            const date = new Date(dateCell);
            if (!isNaN(date.getTime())) {
                return formatDateToISO(date);
            }
        }

        return null;
    } catch (error) {
        console.error(`Error parsing date "${dateCell}":`, error);
        return null;
    }
}

/**
 * Parse DSAT value, returning null for blank/missing values
 */
function parseDSAT(dsatCell: unknown): number | null {
    // Only these cases should return null (blank/missing)
    if (dsatCell === undefined || dsatCell === null || dsatCell === '') {
        return null; // Blank/missing DSAT should be null
    }

    // Handle string percentages
    if (typeof dsatCell === 'string') {
        const cleaned = dsatCell.replace(/[%\s]/g, '');
        if (cleaned === '') {
            return null; // Empty string after cleaning
        }

        const num = parseFloat(cleaned);
        const result = isNaN(num) ? null : num;
        return result; // This will properly handle "0.00%" -> 0
    }

    // Handle numeric values - Excel may store percentages as decimals
    if (typeof dsatCell === 'number') {
        // If the number is between 0 and 1 (inclusive), it's likely a decimal percentage
        // 0 should be treated as 0%, 1.0 should be treated as 100%
        if (dsatCell >= 0 && dsatCell <= 1) {
            // Convert decimal to percentage (multiply by 100)
            const result = dsatCell * 100; // This will correctly handle 0 -> 0
            return result;
        }
        // If it's already > 1, treat it as a regular percentage value
        return dsatCell;
    }

    return null;
}

/**
 * Parse surveys count
 */
function parseSurveys(surveysCell: unknown): number {
    if (surveysCell === undefined || surveysCell === null || surveysCell === '') {
        return 0;
    }

    const num = typeof surveysCell === 'number' ? surveysCell : parseFloat(String(surveysCell));
    return isNaN(num) ? 0 : Math.floor(num);
}

/**
 * Format date to ISO string (YYYY-MM-DD)
 */
function formatDateToISO(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

/**
 * Convert the new format to the existing DailyDSAT format for compatibility
 */
export function convertToLegacyFormat(parsedData: ParsedDailyData): {
    dailyDSAT: Array<{ name: string; dates: { [date: string]: number | null } }>;
    dates: string[];
    mtdData: Array<{ name: string; dsat: number | null; totalSurveys: number; positiveSurveys: number; negativeSurveys: number }>;
} {
    const result = {
        dailyDSAT: [] as Array<{ name: string; dates: { [date: string]: number | null } }>,
        dates: parsedData.dates,
        mtdData: [] as Array<{ name: string; dsat: number | null; totalSurveys: number; positiveSurveys: number; negativeSurveys: number }>
    };

    // Group records by agent
    const agentData: { [agentName: string]: { [date: string]: number | null } } = {};

    parsedData.records.forEach(record => {
        if (!agentData[record.agent]) {
            agentData[record.agent] = {};
        }
        agentData[record.agent][record.date] = record.dsat;
    });

    // Convert to legacy format
    parsedData.agents.forEach(agentName => {
        const agentEntry = {
            name: agentName,
            dates: {} as { [date: string]: number | null }
        };

        // Initialize all dates, properly handling 0 values
        parsedData.dates.forEach(date => {
            const value = agentData[agentName]?.[date];
            // Use ?? instead of || to properly handle 0 values
            agentEntry.dates[date] = value ?? null;
        });

        result.dailyDSAT.push(agentEntry);
    });

    // Convert MTD data to legacy format
    result.mtdData = parsedData.mtdData.map(mtd => ({
        name: mtd.agent,
        dsat: mtd.mtdDSAT,
        totalSurveys: mtd.totalSurveys,
        positiveSurveys: mtd.totalPositiveSurveys,
        negativeSurveys: mtd.totalNegativeSurveys
    }));

    return result;
}

/**
 * Calculate positive and negative surveys based on DSAT percentage
 */
function calculateSurveyBreakdown(totalSurveys: number, dsatPercentage: number | null): {
    positiveSurveys: number;
    negativeSurveys: number;
} {
    if (dsatPercentage === null || totalSurveys === 0) {
        return {
            positiveSurveys: totalSurveys,
            negativeSurveys: 0
        };
    }

    // Calculate negative surveys: Total Surveys * DSAT%
    const negativeSurveys = Math.round(totalSurveys * (dsatPercentage / 100));
    const positiveSurveys = totalSurveys - negativeSurveys;

    return {
        positiveSurveys: Math.max(0, positiveSurveys),
        negativeSurveys: Math.max(0, negativeSurveys)
    };
}

/**
 * Calculate MTD data for all agents
 */
function calculateMTDData(records: DailyDSATRecord[]): AgentMTDData[] {
    const agentTotals: {
        [agent: string]: {
            totalSurveys: number;
            totalPositiveSurveys: number;
            totalNegativeSurveys: number;
        }
    } = {};

    // Aggregate survey data by agent
    records.forEach(record => {
        if (!agentTotals[record.agent]) {
            agentTotals[record.agent] = {
                totalSurveys: 0,
                totalPositiveSurveys: 0,
                totalNegativeSurveys: 0
            };
        }

        agentTotals[record.agent].totalSurveys += record.surveys;
        agentTotals[record.agent].totalPositiveSurveys += record.positiveSurveys;
        agentTotals[record.agent].totalNegativeSurveys += record.negativeSurveys;
    });

    // Calculate MTD DSAT for each agent
    return Object.entries(agentTotals).map(([agent, totals]) => {
        const mtdDSAT = totals.totalSurveys > 0
            ? (totals.totalNegativeSurveys / totals.totalSurveys) * 100
            : null;

        return {
            agent,
            totalSurveys: totals.totalSurveys,
            totalPositiveSurveys: totals.totalPositiveSurveys,
            totalNegativeSurveys: totals.totalNegativeSurveys,
            mtdDSAT
        };
    });
}

/**
 * Convert decimal minutes to MM:SS format
 * Example: 10.90 -> "10:54" (10 minutes + 0.90 * 60 seconds)
 */
export function convertDecimalMinutesToTime(decimalMinutes: number): string {
    const minutes = Math.floor(decimalMinutes);
    const seconds = Math.round((decimalMinutes - minutes) * 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
}

/**
 * Parse AHT value from various formats
 */
function parseAHT(ahtCell: unknown): number | null {
    if (ahtCell === undefined || ahtCell === null || ahtCell === '') {
        return null;
    }

    // Handle numeric values (decimal minutes)
    if (typeof ahtCell === 'number') {
        return ahtCell;
    }

    // Handle string values
    if (typeof ahtCell === 'string') {
        // Remove any non-numeric characters except decimal point
        const cleaned = ahtCell.replace(/[^\d.]/g, '');
        if (cleaned === '') return null;

        const num = parseFloat(cleaned);
        return isNaN(num) ? null : num;
    }

    return null;
}

/**
 * Parse the MTD DSAT supervisor table format where:
 * - Row 1: "MTD DSAT" in A1
 * - Column A: Agent names
 * - Column B: DSAT percentages  
 * - Column C: AHT values (decimal minutes)
 */
export function parseMTDSupervisorFormat(buffer: ArrayBuffer): {
    agents: AgentMTDData[];
    teamPerformance: { dsat: number; aht: number; qa?: number } | null;
} {
    const result: AgentMTDData[] = [];
    let teamPerformanceData: { dsat: number; aht: number; qa?: number } | null = null;

    try {
        // Load the workbook
        const workbook = XLSX.read(buffer, { type: 'array' });

        // Get first sheet
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];

        // Convert to JSON with header:1 to get array of arrays
        const data = XLSX.utils.sheet_to_json<unknown[]>(worksheet, { header: 1 });

        if (data.length < 2) {
            // Not enough rows for MTD Supervisor format
            return { agents: result, teamPerformance: teamPerformanceData };
        }

        // Process all rows to find both individual agents and team performance
        for (let rowIndex = 1; rowIndex < data.length; rowIndex++) {
            const row = data[rowIndex];
            if (!row || !Array.isArray(row) || row.length === 0) continue;

            // Get agent name from first column
            const agentCell = row[0];
            if (!agentCell) continue;

            const agentName = String(agentCell).trim();
            if (!agentName || agentName.toLowerCase().includes('mtd dsat')) continue;

            // Check if this is a "Total" row for team performance
            if (agentName.toLowerCase() === 'total') {
                // Extract team performance data from Total row
                const dsatCell = row[1];
                const ahtCell = row[2];
                const qaCell = row[3]; // QA might be in column D

                const teamDSAT = parseDSAT(dsatCell);
                const teamAHT = parseAHT(ahtCell);
                const teamQA = qaCell ? parseDSAT(qaCell) : null; // Use same parser for QA percentage

                if (teamDSAT !== null || teamAHT !== null) {
                    teamPerformanceData = {
                        dsat: teamDSAT || 0,
                        aht: teamAHT || 0,
                        qa: teamQA || undefined
                    };
                }
                continue; // Skip adding Total row to individual agents
            }

            // Parse DSAT from column B (index 1)
            const dsatCell = row[1];
            const mtdDSATValue = parseDSAT(dsatCell);

            // Parse AHT from column C (index 2)
            const ahtCell = row[2];
            const ahtValue = parseAHT(ahtCell);

            // Parse QA from column D (index 3) if available
            const qaCell = row[3];
            const qaValue = qaCell ? parseDSAT(qaCell) : null; // Use same parser for QA percentage

            // Only create record if we have at least DSAT or AHT data
            if (mtdDSATValue !== null || ahtValue !== null || qaValue !== null) {
                const mtdData: AgentMTDData = {
                    agent: agentName,
                    totalSurveys: 0, // MTD files don't contain survey data
                    totalPositiveSurveys: 0,
                    totalNegativeSurveys: 0,
                    mtdDSAT: mtdDSATValue,
                    aht: ahtValue,
                    qa: qaValue
                };

                result.push(mtdData);
            }
        }

        return { agents: result, teamPerformance: teamPerformanceData };

    } catch (error) {
        console.error("Error parsing MTD supervisor XLSX format:", error);
        return { agents: result, teamPerformance: teamPerformanceData };
    }
}

// Update the existing interfaces to support file type detection
export interface ParsedFileData {
    type: 'mtd' | 'aht' | 'qa' | 'apm-data' | 'bi-data' | 'mtd-bi-data';
    dailyData?: ParsedDailyData;
    mtdData?: AgentMTDData[] | MTDRanking[]; // Support both types
    ahtData?: AgentMTDData[];
    qaData?: AgentMTDData[];
    mtdTeamPerformance?: { dsat: number; aht: number; qa?: number } | null; // MTD team performance from Total row
}

/**
      * Enhanced auto-detect function that can handle AHT, QA, APM Data, and Daily BI Data formats
 * Uses filename-based detection first, then falls back to content-based detection
 */
export function parseXLSXAutoEnhanced(buffer: ArrayBuffer, filename?: string): ParsedFileData {
    try {
        // Load the workbook for format detection
        const workbook = XLSX.read(buffer, { type: 'array' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const data = XLSX.utils.sheet_to_json<unknown[]>(worksheet, { header: 1 });

        if (data.length === 0) {
            // Empty sheet detected
            return { type: 'apm-data', dailyData: { records: [], dates: [], agents: [], mtdData: [] } };
        }

        // PRIORITY 1: Filename-based detection (most reliable)
        if (filename) {
            const fileNameLower = filename.toLowerCase();
            console.log('Auto-detect: Checking filename patterns for:', filename);

            // Daily BI Data format: files starting with "data"
            if (fileNameLower.startsWith('data')) {
                console.log('Auto-detect: Daily BI Data format detected by filename pattern');
                const dailyData = parseBIDataFormat(buffer);
                return { type: 'bi-data', dailyData };
            }

            // MTD BI Data format: files starting with "customer support"
            if (fileNameLower.startsWith('customer support')) {
                console.log('Auto-detect: MTD BI Data format detected by filename pattern');
                const mtdBIResult = parseMTDBIDataFormatSimple(buffer);
                return {
                    type: 'mtd-bi-data',
                    mtdData: mtdBIResult.agents,
                    mtdTeamPerformance: mtdBIResult.teamPerformance
                };
            }

            // AHT format: files containing "aht" or "handle time"
            if (fileNameLower.includes('aht') || fileNameLower.includes('handle') || fileNameLower.includes('time')) {
                console.log('Auto-detect: AHT format detected by filename pattern');
                const ahtData = parseAHTFormat(buffer);
                return { type: 'aht', ahtData };
            }

            // QA format: files containing "agent scorecard" or "scorecard"
            if (fileNameLower.includes('agent scorecard') || fileNameLower.includes('scorecard')) {
                console.log('Auto-detect: QA format detected by filename pattern');
                const qaData = parseQAFormat(buffer);
                return { type: 'qa', qaData };
            }

            // APM Data format: files containing "DSAT by leader" or "DSAT"
            if (fileNameLower.includes('dsat by leader') || fileNameLower.includes('dsat')) {
                console.log('Auto-detect: APM Data format detected by filename pattern');
                const dailyData = parseNewXLSXFormat(buffer);
                return { type: 'apm-data', dailyData };
            }

            console.log('Auto-detect: No filename pattern matched, proceeding to content-based detection');
        } else {
            console.log('Auto-detect: No filename provided, using content-based detection');
        }

        // PRIORITY 2: Content-based detection (fallback when filename doesn't match patterns)
        console.log('Auto-detect: Starting content-based detection');

        // Check for supervisor format indicators
        const firstRow = data[0];

        if (firstRow && Array.isArray(firstRow)) {
            const firstCell = firstRow[0];

            if (firstCell) {
                const cellStr = String(firstCell).toLowerCase();
                console.log('Auto-detect: First cell content:', cellStr);

                // Check for MTD DSAT format
                if (cellStr.includes('mtd dsat')) {
                    console.log('Auto-detect: MTD DSAT format detected');
                    const mtdResult = parseMTDSupervisorFormat(buffer);
                    return {
                        type: 'mtd',
                        mtdData: mtdResult.agents,
                        mtdTeamPerformance: mtdResult.teamPerformance
                    };
                }

                // Check for AHT/QA format - look for month/year patterns OR AHT/QA indicators
                if (cellStr.includes('january') || cellStr.includes('february') || cellStr.includes('march') ||
                    cellStr.includes('april') || cellStr.includes('may') || cellStr.includes('june') ||
                    cellStr.includes('july') || cellStr.includes('august') || cellStr.includes('september') ||
                    cellStr.includes('october') || cellStr.includes('november') || cellStr.includes('december') ||
                    cellStr.includes('date') || cellStr.includes('month')) {

                    console.log('Auto-detect: Month/date pattern found, checking for AHT/QA indicators');

                    // Additional check: look for AHT indicators in header row
                    let hasAHTHeader = false;
                    if (firstRow.length >= 3) {
                        const thirdColumnHeader = String(firstRow[2] || '').toLowerCase();
                        console.log('Auto-detect: Third column header:', thirdColumnHeader);
                        if (thirdColumnHeader.includes('aht') || thirdColumnHeader.includes('chat aht') ||
                            thirdColumnHeader.includes('handle time') || thirdColumnHeader.includes('avg handle')) {
                            hasAHTHeader = true;
                            console.log('Auto-detect: AHT header detected in third column');
                        }
                    }

                    // Additional check: look for duration format patterns (AHT) or percentage patterns (QA) in the third column
                    let hasDurationFormat = false;
                    let hasPercentageFormat = false;

                    for (let i = 1; i < Math.min(5, data.length); i++) {
                        const row = data[i];
                        if (row && Array.isArray(row) && row[2]) {
                            const cellValue = String(row[2]);
                            console.log(`Auto-detect: Row ${i}, column C content:`, cellValue);
                            // Check if it looks like a duration (contains colons)
                            if (cellValue.includes(':') && /\d+:\d+/.test(cellValue)) {
                                hasDurationFormat = true;
                                console.log('Auto-detect: Duration format detected in data');
                                break;
                            }
                            // Check if it looks like a percentage (ends with % or is a decimal between 0-1)
                            if (cellValue.includes('%') || /^0\.\d+$/.test(cellValue) ||
                                (/^\d+(\.\d+)?$/.test(cellValue) && parseFloat(cellValue) <= 100)) {
                                hasPercentageFormat = true;
                                console.log('Auto-detect: Percentage format detected in data');
                            }
                        }
                    }

                    if (hasDurationFormat || hasAHTHeader) {
                        console.log('Auto-detect: AHT format detected');
                        const ahtData = parseAHTFormat(buffer);
                        return { type: 'aht', ahtData };
                    } else if (hasPercentageFormat) {
                        console.log('Auto-detect: QA format detected');
                        const qaData = parseQAFormat(buffer);
                        return { type: 'qa', qaData };
                    } else {
                        console.log('Auto-detect: Month/date pattern found but no AHT/QA indicators');
                    }
                } else {
                    console.log('Auto-detect: No month/date pattern found in first cell');
                }
            } else {
                console.log('Auto-detect: First cell is empty');
            }
        } else {
            console.log('Auto-detect: First row is invalid or empty');
        }

        // Check for QA format by scanning multiple rows for "Average Quality Score"
        console.log('Auto-detect: Scanning for QA format patterns...');
        for (let rowIndex = 0; rowIndex < Math.min(3, data.length); rowIndex++) {
            const row = data[rowIndex];
            if (row && Array.isArray(row)) {
                for (let colIndex = 0; colIndex < row.length; colIndex++) {
                    const cellValue = String(row[colIndex] || '').toLowerCase();
                    console.log(`Auto-detect: Row ${rowIndex}, Column ${colIndex}: "${cellValue}"`);

                    if (cellValue.includes('average quality score') ||
                        cellValue.includes('quality score') ||
                        cellValue.includes('qa score')) {
                        console.log(`Auto-detect: QA header found at row ${rowIndex}, column ${colIndex}`);

                        // Verify there's QA data in subsequent rows
                        let hasQAData = false;
                        for (let dataRowIndex = rowIndex + 1; dataRowIndex < Math.min(rowIndex + 5, data.length); dataRowIndex++) {
                            const dataRow = data[dataRowIndex];
                            if (dataRow && Array.isArray(dataRow) && dataRow[colIndex]) {
                                const qaValue = String(dataRow[colIndex]);
                                console.log(`Auto-detect: Checking QA data at row ${dataRowIndex}, column ${colIndex}: "${qaValue}"`);

                                // Check for QA patterns: "1" (100%), percentages, or decimal values
                                if (qaValue === '1' || qaValue.includes('%') ||
                                    (/^\d+(\.\d+)?$/.test(qaValue) && parseFloat(qaValue) >= 0 && parseFloat(qaValue) <= 100)) {
                                    hasQAData = true;
                                    console.log('Auto-detect: QA data pattern confirmed');
                                    break;
                                }
                            }
                        }

                        if (hasQAData) {
                            console.log('Auto-detect: QA format detected');
                            const qaData = parseQAFormat(buffer);
                            return { type: 'qa', qaData };
                        }
                    }
                }
            }
        }

        // Check for APM Data format indicators (look for column headers)
        for (let i = 0; i < Math.min(5, data.length); i++) {
            const row = data[i];
            if (!row || !Array.isArray(row)) continue;

            const rowStr = row.map(cell => String(cell || '').toLowerCase()).join(' ');

            // Look for characteristic column headers of APM Data format
            if (rowStr.includes('utc date') ||
                (rowStr.includes('agent') && rowStr.includes('dsat') && rowStr.includes('survey'))) {
                console.log('Auto-detect: APM Data format detected');
                const dailyData = parseNewXLSXFormat(buffer);
                return { type: 'apm-data', dailyData };
            }
        }

        // Default fallback: APM Data format
        console.log('Auto-detect: Format unclear, defaulting to APM Data format');
        const apmDataResult = parseNewXLSXFormat(buffer);
        return { type: 'apm-data', dailyData: apmDataResult };

    } catch (error) {
        console.error("Error in enhanced auto-detection:", error);
        // Final fallback to APM Data format
        const fallbackResult = parseNewXLSXFormat(buffer);
        return { type: 'apm-data', dailyData: fallbackResult };
    }
}

/**
 * Convert duration format (H:MM:SS or MM:SS) to decimal minutes
 * Examples: "0:10:45" -> 10.75, "10:45" -> 10.75, "1:05:30" -> 65.5
 */
function convertDurationToDecimalMinutes(duration: string): number | null {
    if (!duration || typeof duration !== 'string') return null;

    // Remove any whitespace
    const cleaned = duration.trim();

    // Split by colon
    const parts = cleaned.split(':');

    if (parts.length === 2) {
        // MM:SS format
        const minutes = parseInt(parts[0], 10);
        const seconds = parseInt(parts[1], 10);

        if (isNaN(minutes) || isNaN(seconds)) return null;

        return minutes + (seconds / 60);
    } else if (parts.length === 3) {
        // H:MM:SS format
        const hours = parseInt(parts[0], 10);
        const minutes = parseInt(parts[1], 10);
        const seconds = parseInt(parts[2], 10);

        if (isNaN(hours) || isNaN(minutes) || isNaN(seconds)) return null;

        return (hours * 60) + minutes + (seconds / 60);
    }

    return null;
}

/**
 * Parse standalone AHT file format where:
 * - Column A: Month and Year (e.g., "June, 2025")
 * - Column B: Agent names
 * - Column C: AHT values in duration format (e.g., "0:10:45")
 */
export function parseAHTFormat(buffer: ArrayBuffer): AgentMTDData[] {
    const result: AgentMTDData[] = [];

    try {
        // Load the workbook
        const workbook = XLSX.read(buffer, { type: 'array' });

        // Get first sheet
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];

        // Convert to JSON with header:1 to get array of arrays
        const data = XLSX.utils.sheet_to_json<unknown[]>(worksheet, { header: 1 });

        console.log('AHT format: Total rows found:', data.length);
        if (data.length > 0) {
            console.log('AHT format: First few rows:', data.slice(0, 3));
        }

        if (data.length < 2) {
            console.log('AHT format: Not enough rows');
            return result;
        }

        // Process agent rows (starting from row 1, assuming row 0 might be headers)
        for (let rowIndex = 1; rowIndex < data.length; rowIndex++) {
            const row = data[rowIndex];
            if (!row || !Array.isArray(row) || row.length < 3) {
                console.log(`AHT format: Skipping row ${rowIndex}, invalid structure:`, row);
                continue;
            }

            // Get agent name from second column (index 1)
            const agentCell = row[1];
            if (!agentCell) {
                console.log(`AHT format: Row ${rowIndex}, no agent name in column B:`, row);
                continue;
            }

            const agentName = String(agentCell).trim();
            if (!agentName) {
                console.log(`AHT format: Row ${rowIndex}, empty agent name:`, row);
                continue;
            }

            // Get AHT value from third column (index 2)
            const ahtCell = row[2];
            if (!ahtCell) {
                console.log(`AHT format: Row ${rowIndex}, no AHT value in column C:`, row);
                continue;
            }

            console.log(`AHT format: Processing row ${rowIndex}, agent: "${agentName}", AHT: "${ahtCell}"`);

            // Convert AHT value to decimal minutes
            let ahtValue: number | null = null;

            if (typeof ahtCell === 'number') {
                // Handle Excel decimal time format (fraction of a day)
                // Convert from fraction of day to minutes: multiply by 1440 (minutes in a day)
                ahtValue = ahtCell * 1440;
                console.log(`AHT format: Converted decimal day fraction ${ahtCell} to ${ahtValue} minutes`);
            } else if (typeof ahtCell === 'string') {
                // Handle duration format strings like "0:10:45"
                ahtValue = convertDurationToDecimalMinutes(ahtCell);
                console.log(`AHT format: Converted duration string "${ahtCell}" to ${ahtValue} minutes`);
            }

            console.log(`AHT format: Final AHT value for ${agentName}:`, ahtValue);

            if (ahtValue !== null) {
                const ahtData: AgentMTDData = {
                    agent: agentName,
                    totalSurveys: 0, // AHT files don't contain survey data
                    totalPositiveSurveys: 0,
                    totalNegativeSurveys: 0,
                    mtdDSAT: null, // No DSAT data in AHT files
                    aht: ahtValue
                };

                result.push(ahtData);
                console.log(`AHT format: Added agent data:`, ahtData);
            } else {
                console.log(`AHT format: Row ${rowIndex}, failed to convert AHT value:`, ahtCell);
            }
        }

        console.log(`AHT format: Parsed ${result.length} AHT records`);
        return result;

    } catch (error) {
        console.error("Error parsing AHT XLSX format:", error);
        return result;
    }
}

/**
 * Parse standalone QA file format where:
 * - Headers can be in any row (typically row 1)
 * - Agent names and QA scores are in subsequent rows
 * - QA values can be "1" (meaning 100%), percentages, or decimal values
 */
export function parseQAFormat(buffer: ArrayBuffer): AgentMTDData[] {
    const result: AgentMTDData[] = [];

    try {
        // Load the workbook
        const workbook = XLSX.read(buffer, { type: 'array' });

        // Get first sheet
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];

        // Convert to JSON with header:1 to get array of arrays
        const data = XLSX.utils.sheet_to_json<unknown[]>(worksheet, { header: 1 });

        console.log('QA format: Total rows found:', data.length);
        console.log('QA format: First few rows:', data.slice(0, 3));

        if (data.length < 2) {
            console.log('QA format: Not enough rows');
            return result;
        }

        // Find the QA header and agent column
        let qaColumnIndex = -1;
        let agentColumnIndex = -1;
        let headerRowIndex = -1;

        // Scan first few rows to find headers
        for (let rowIndex = 0; rowIndex < Math.min(3, data.length); rowIndex++) {
            const row = data[rowIndex];
            if (row && Array.isArray(row)) {
                for (let colIndex = 0; colIndex < row.length; colIndex++) {
                    const cellValue = String(row[colIndex] || '').toLowerCase();

                    if (cellValue.includes('average quality score') ||
                        cellValue.includes('quality score') ||
                        cellValue.includes('qa score')) {
                        qaColumnIndex = colIndex;
                        headerRowIndex = rowIndex;
                        console.log(`QA format: Found QA header at row ${rowIndex}, column ${colIndex}`);
                    } else if (cellValue.includes('agent') || cellValue.includes('name')) {
                        agentColumnIndex = colIndex;
                        console.log(`QA format: Found Agent header at row ${rowIndex}, column ${colIndex}`);
                    }
                }
            }
        }

        // If no specific agent column found, try column 1 (common pattern)
        if (agentColumnIndex === -1) {
            agentColumnIndex = 1;
            console.log('QA format: Using default agent column index 1');
        }

        // If no QA column found, exit
        if (qaColumnIndex === -1) {
            console.log('QA format: No QA column found');
            return result;
        }

        console.log(`QA format: Using QA column ${qaColumnIndex}, Agent column ${agentColumnIndex}, Header row ${headerRowIndex}`);

        // Process data rows (starting after the header row)
        const startRowIndex = Math.max(1, headerRowIndex + 1);
        for (let rowIndex = startRowIndex; rowIndex < data.length; rowIndex++) {
            const row = data[rowIndex];
            if (!row || !Array.isArray(row)) {
                console.log(`QA format: Skipping invalid row ${rowIndex}`);
                continue;
            }

            // Get agent name
            const agentCell = row[agentColumnIndex];
            if (!agentCell) {
                console.log(`QA format: No agent name in row ${rowIndex}, column ${agentColumnIndex}`);
                continue;
            }

            const agentName = String(agentCell).trim();
            if (!agentName || agentName.toLowerCase().includes('agent')) {
                // Skip header rows or empty names
                console.log(`QA format: Skipping header/empty agent name: "${agentName}"`);
                continue;
            }

            // Get QA value
            const qaCell = row[qaColumnIndex];
            if (qaCell === null || qaCell === undefined) {
                console.log(`QA format: No QA value in row ${rowIndex}, column ${qaColumnIndex}`);
                continue;
            }

            console.log(`QA format: Processing row ${rowIndex} - Agent: "${agentName}", QA: "${qaCell}"`);

            // Parse QA percentage
            let qaPercentage: number | null = null;

            if (typeof qaCell === 'string') {
                const cleanStr = qaCell.replace('%', '').trim();
                if (cleanStr === '1') {
                    // Special case: "1" means 100%
                    qaPercentage = 100;
                } else {
                    const value = parseFloat(cleanStr);
                    if (!isNaN(value)) {
                        qaPercentage = value <= 1 ? value * 100 : value;
                    }
                }
            } else if (typeof qaCell === 'number') {
                if (qaCell === 1) {
                    // Special case: numeric 1 means 100%
                    qaPercentage = 100;
                } else {
                    qaPercentage = qaCell <= 1 ? qaCell * 100 : qaCell;
                }
            }

            console.log(`QA format: Converted "${qaCell}" to ${qaPercentage}%`);

            // Validate QA percentage range (0-100)
            if (qaPercentage !== null && qaPercentage >= 0 && qaPercentage <= 100) {
                const qaData: AgentMTDData = {
                    agent: agentName,
                    totalSurveys: 0,
                    totalPositiveSurveys: 0,
                    totalNegativeSurveys: 0,
                    mtdDSAT: null,
                    qa: qaPercentage
                };

                result.push(qaData);
                console.log(`QA format: Added ${agentName} with QA ${qaPercentage}%`);
            } else {
                console.log(`QA format: Invalid QA percentage for ${agentName}: ${qaPercentage}`);
            }
        }

        console.log(`QA format: Successfully parsed ${result.length} QA records`);
        return result;

    } catch (error) {
        console.error("Error parsing QA XLSX format:", error);
        return result;
    }
}

/**
 * Parse Daily BI Data format (supervisor export) where:
 * - File name starts with "data"
 * - Row 1: Dates starting from column C
 * - Row 2: Agents starting from column B  
 * - Row 3: "Total" row with team performance data
 * - Row 4+: Individual agent data
 * - Data format: DSAT percentages (0-100)
 */
export function parseBIDataFormat(buffer: ArrayBuffer): ParsedDailyData {
    const result: ParsedDailyData = {
        records: [],
        dates: [],
        agents: [],
        mtdData: []
    };

    try {
        // Load the workbook
        const workbook = XLSX.read(buffer, { type: 'array' });

        // Get first sheet
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];

        // Convert to JSON with header:1 to get array of arrays
        const data = XLSX.utils.sheet_to_json<unknown[]>(worksheet, { header: 1 });

        if (data.length < 4) {
            console.log('Daily BI Data format: Not enough rows');
            return result;
        }

        // Get dates from row 1 (index 0) starting from column C (index 2)
        const headerRow = data[0];
        if (!headerRow || !Array.isArray(headerRow) || headerRow.length < 3) {
            console.log('Daily BI Data format: Invalid header row');
            return result;
        }

        // Parse dates from header (starting from column C, index 2)
        const dates: string[] = [];
        const dateColumnMap: { [colIndex: number]: string } = {};

        for (let colIndex = 2; colIndex < headerRow.length; colIndex++) {
            const dateCell = headerRow[colIndex];
            if (dateCell) {
                const parsedDate = parseDate(dateCell);
                if (parsedDate) {
                    dates.push(parsedDate);
                    dateColumnMap[colIndex] = parsedDate;
                }
            }
        }

        if (dates.length === 0) {
            console.log('Daily BI Data format: No valid dates found in header');
            return result;
        }

        console.log(`Daily BI Data format: Found ${dates.length} dates:`, dates);

        // Get Team Performance data from row 3 (index 2) - the "Total" row
        const teamRow = data[2];
        const teamPerformanceData: { [date: string]: number | null } = {};
        if (teamRow && Array.isArray(teamRow)) {
            // Process Team Performance DSAT values for each date
            for (const [colIndex, dateStr] of Object.entries(dateColumnMap)) {
                const colNum = parseInt(colIndex);
                const dsatCell = teamRow[colNum];

                // Parse DSAT value (can be null/blank)
                const dsatValue = parseDSAT(dsatCell);
                teamPerformanceData[dateStr] = dsatValue;
            }

            result.teamPerformanceData = teamPerformanceData;
            console.log(`Daily BI Data format: Stored Team Performance data from Total row for UI calculation override`);
        }

        // Get agent names from column B (index 1) starting from row 4 (index 3)
        const uniqueAgents = new Set<string>(); // Start with team performance

        for (let rowIndex = 3; rowIndex < data.length; rowIndex++) {
            const row = data[rowIndex];
            if (!row || !Array.isArray(row) || row.length === 0) continue;

            // Get agent name from column B (index 1)
            const agentCell = row[1];
            if (!agentCell) {
                // Reached blank cell, stop agent parsing
                break; // Stop when we reach a blank cell
            }

            const agentName = String(agentCell).trim();
            if (!agentName || agentName.toLowerCase() === 'total') {
                continue; // Skip empty names or "Total" rows
            }

            uniqueAgents.add(agentName);

            // Process DSAT values for each date
            for (const [colIndex, dateStr] of Object.entries(dateColumnMap)) {
                const colNum = parseInt(colIndex);
                const dsatCell = row[colNum];

                // Parse DSAT value (can be null/blank)
                const dsatValue = parseDSAT(dsatCell);

                // Create record with no survey data (will show as "-" in UI)
                const record: DailyDSATRecord = {
                    date: dateStr,
                    agent: agentName,
                    dsat: dsatValue,
                    surveys: 0, // No survey data in BI format
                    positiveSurveys: 0,
                    negativeSurveys: 0
                };

                result.records.push(record);
            }
        }

        // Sort dates and agents
        result.dates = dates.sort((a, b) => new Date(a).getTime() - new Date(b).getTime());
        result.agents = Array.from(uniqueAgents).sort();

        // No MTD calculations for Daily BI Data format (will show as "-")
        result.mtdData = [];

        // Parsing complete

        return result;

    } catch (error) {
        console.error("Error parsing Daily BI Data format:", error);
        return result;
    }
}

/**
 * Parse MTD BI Data format (supervisor export) - Enhanced wrapper
 * Returns both MTDRanking[] and team performance data for proper display
 */
function parseMTDBIDataFormatSimple(buffer: ArrayBuffer): {
    agents: MTDRanking[];
    teamPerformance: { dsat: number; aht: number; qa?: number } | null;
} {
    try {
        const workbook = XLSX.read(buffer, { type: 'buffer' });
        const worksheet = workbook.Sheets[workbook.SheetNames[0]];
        const data = XLSX.utils.sheet_to_json(worksheet, { header: 1, defval: null }) as unknown[][];

        const result: MTDRanking[] = [];
        let teamPerformanceData: { dsat: number; aht: number; qa?: number } | null = null;

        if (data.length < 2) {
            return { agents: result, teamPerformance: teamPerformanceData };
        }

        // Skip patterns for header and system rows
        const skipPatterns = [
            'alvaria name',
            'agent name',
            'name',
            'supervisor',
            'team',
            'department',
            'dsat',
            'aht'
        ];

        // First pass: Find the first individual agent (not supervisor/header/total)
        let firstAgentRowIndex = -1;
        for (let rowIndex = 0; rowIndex < data.length; rowIndex++) {
            const row = data[rowIndex];
            if (!row || !Array.isArray(row) || row.length < 4) continue;

            const agentCell = row[1];
            if (!agentCell) continue;

            const agentName = String(agentCell).trim();
            if (!agentName) continue;

            // Skip headers, system fields, and "Total" rows
            if (agentName.toLowerCase() === 'total' ||
                skipPatterns.some(pattern => agentName.toLowerCase().includes(pattern))) {
                continue;
            }

            // This is the first individual agent
            firstAgentRowIndex = rowIndex;
            break;
        }

        // Second pass: Find the "Total" row immediately before the first agent for team performance
        if (firstAgentRowIndex > 0) {
            // Look for the "Total" row immediately before the first agent
            for (let rowIndex = firstAgentRowIndex - 1; rowIndex >= 0; rowIndex--) {
                const row = data[rowIndex];
                if (!row || !Array.isArray(row) || row.length < 4) continue;

                const agentCell = row[1];
                if (!agentCell) continue;

                const agentName = String(agentCell).trim();
                if (agentName.toLowerCase() === 'total') {
                    const dsatValue = row[2]; // Column C
                    const ahtValue = row[3];  // Column D

                    if (dsatValue !== null && dsatValue !== undefined && !isNaN(Number(dsatValue))) {
                        let dsat = Number(dsatValue);

                        // If the value is a small decimal (< 1), convert to percentage
                        if (dsat < 1 && dsat > 0) {
                            dsat = dsat * 100;
                        }

                        let aht = 0;
                        if (ahtValue !== null && ahtValue !== undefined && !isNaN(Number(ahtValue))) {
                            aht = Number(ahtValue);
                        }

                        teamPerformanceData = { dsat, aht };
                    }
                    break; // Found the supervisor team total, stop looking
                }
            }
        }

        // Third pass: Process all individual agents starting from the first agent row
        if (firstAgentRowIndex >= 0) {
            for (let rowIndex = firstAgentRowIndex; rowIndex < data.length; rowIndex++) {
                const row = data[rowIndex];
                if (!row || !Array.isArray(row) || row.length < 4) continue;

                const agentCell = row[1];
                if (!agentCell) continue;

                const agentName = String(agentCell).trim();
                if (!agentName) continue;

                // Skip "Total" rows and header/system fields
                if (agentName.toLowerCase() === 'total' ||
                    skipPatterns.some(pattern => agentName.toLowerCase().includes(pattern))) {
                    continue;
                }

                // Processing individual agent data
                const dsatValue = row[2]; // Column C - DSAT MTD
                const ahtValue = row[3];  // Column D - AHT MTD

                // Create MTD data entry for this agent
                const mtdEntry: MTDRanking = {
                    name: agentName,
                    dsat: 0,
                    aht: null,
                    qa: null
                };

                // Parse DSAT value and convert to percentage if needed
                if (dsatValue !== null && dsatValue !== undefined && !isNaN(Number(dsatValue))) {
                    let dsat = Number(dsatValue);

                    // If the value is a small decimal (< 1), convert to percentage
                    if (dsat < 1 && dsat > 0) {
                        dsat = dsat * 100;
                    }

                    mtdEntry.dsat = dsat;
                }

                // Parse AHT value (decimal minutes)
                if (ahtValue !== null && ahtValue !== undefined && !isNaN(Number(ahtValue))) {
                    mtdEntry.aht = Number(ahtValue);
                }

                result.push(mtdEntry);
            }
        }



        // Parsing complete

        return { agents: result, teamPerformance: teamPerformanceData };

    } catch (error) {
        console.error("Error parsing MTD BI Data format:", error);
        return { agents: [], teamPerformance: null };
    }
}

