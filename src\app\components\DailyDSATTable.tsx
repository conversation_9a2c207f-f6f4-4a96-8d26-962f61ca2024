"use client";
import React, { useState, useMemo, useEffect, useRef, useCallback } from "react";
import { DailyDSAT, MTDRanking } from "@/app/lib/parseKPI";
import { DateRangePicker } from "./DateRangePicker";
import { DailyDSATPoster } from "./DailyDSATPoster";
import clsx from "clsx";
import { useDepartment } from './DepartmentContext';
import { PerformanceStatus } from "./PerformanceStatus";

// Interface for daily survey data with actual survey counts
interface DailySurveyData {
    date: string;
    agent: string;
    dsat: number | null;
    surveys: number;
}

interface DailyDSATTableProps {
    data: DailyDSAT[];
    dates: string[];
    mtdData?: MTDRanking[]; // Optional MTD data for authoritative averages
    dailySurveyData?: DailySurveyData[]; // Optional: Raw daily survey data for accurate calculations
    teamPerformanceData?: { [date: string]: number | null }; // Optional: Team Performance from Daily BI Data Total row
}

function getPerformanceColor(score: number | null, excellent: number, good: number, target: number): string {
    if (score === null) return "text-gray-400"; // Gray for missing data

    // Department-specific tier-based color system (lower DSAT is better)
    if (score <= excellent) return "text-green-600 dark:text-green-400"; // Tier 1 - Green
    if (score <= good) return "text-yellow-600 dark:text-yellow-400"; // Tier 2 - Yellow
    if (score <= target) return "text-orange-600 dark:text-orange-400"; // Tier 3 - Orange
    return "text-red-600 dark:text-red-400"; // Above Tier 3 - Red
}

function getPerformanceBg(score: number | null, excellent: number, good: number, target: number): string {
    if (score === null) return "bg-gray-50 dark:bg-gray-800"; // Gray background for missing data

    // Department-specific tier-based color system (lower DSAT is better)
    if (score <= excellent) return "bg-green-50 dark:bg-green-900/20"; // Tier 1 - Green
    if (score <= good) return "bg-yellow-50 dark:bg-yellow-900/20"; // Tier 2 - Yellow
    if (score <= target) return "bg-orange-50 dark:bg-orange-900/20"; // Tier 3 - Orange
    return "bg-red-50 dark:bg-red-900/20"; // Above Tier 3 - Red
}

// Multi-select agent filter component
interface AgentMultiSelectProps {
    agents: string[];
    selectedAgents: string[];
    onSelectionChange: (selected: string[]) => void;
    performanceFilter: "all" | "excellent" | "good" | "warning" | "poor";
    onPerformanceFilterChange: (filter: "all" | "excellent" | "good" | "warning" | "poor") => void;
    performanceStats: {
        totalExcellent: number;
        totalGood: number;
        totalWarning: number;
        totalPoor: number;
    };
    config: {
        dsat: {
            excellent: number;
            good: number;
            target: number;
        };
    };
    data: DailyDSAT[];
    displayDates: string[];
    getAgentAverage: (agent: DailyDSAT, datesToUse: string[]) => number | null;
}

const AgentMultiSelect: React.FC<AgentMultiSelectProps> = ({
    agents,
    selectedAgents,
    onSelectionChange,
    performanceFilter,
    onPerformanceFilterChange,
    performanceStats,
    config,
    data,
    displayDates,
    getAgentAverage
}) => {
    const [isOpen, setIsOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState("");
    const dropdownRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const handleToggleAgent = (agentName: string) => {
        if (selectedAgents.includes(agentName)) {
            onSelectionChange(selectedAgents.filter(name => name !== agentName));
        } else {
            onSelectionChange([...selectedAgents, agentName]);
        }
    };

    const handleSelectAll = () => {
        onSelectionChange(data.map(agent => agent.name));
    };

    const handleSelectNone = () => {
        onSelectionChange([]);
    };

    const handlePerformanceTierSelect = (tier: "all" | "excellent" | "good" | "warning" | "poor") => {
        if (tier === "all") {
            // Perform the same action as "Select All" button
            handleSelectAll();
        } else {
            // Find agents in the selected tier and replace the selection
            const agentsInTier = data.filter(agent => {
                const avgScore = getAgentAverage(agent, displayDates);

                if (avgScore === null) {
                    // If no data, consider as poor performance
                    return tier === "poor";
                }

                switch (tier) {
                    case "excellent":
                        return avgScore <= config.dsat.excellent; // Tier 1
                    case "good":
                        return avgScore > config.dsat.excellent && avgScore <= config.dsat.good; // Tier 2
                    case "warning":
                        return avgScore > config.dsat.good && avgScore <= config.dsat.target; // Tier 3
                    case "poor":
                        return avgScore > config.dsat.target; // Above Tier 3
                    default:
                        return false;
                }
            }).map(agent => agent.name);

            onSelectionChange(agentsInTier);
        }
        onPerformanceFilterChange(tier);
    };

    const handleAddTierAgents = (tier: "excellent" | "good" | "warning" | "poor") => {
        // Find agents in the selected tier and replace the selection (same as handlePerformanceTierSelect)
        const agentsInTier = data.filter(agent => {
            const avgScore = getAgentAverage(agent, displayDates);

            if (avgScore === null) {
                // If no data, consider as poor performance
                return tier === "poor";
            }

            switch (tier) {
                case "excellent":
                    return avgScore <= config.dsat.excellent; // Tier 1
                case "good":
                    return avgScore > config.dsat.excellent && avgScore <= config.dsat.good; // Tier 2
                case "warning":
                    return avgScore > config.dsat.good && avgScore <= config.dsat.target; // Tier 3
                case "poor":
                    return avgScore > config.dsat.target; // Above Tier 3
                default:
                    return false;
            }
        }).map(agent => agent.name);

        onSelectionChange(agentsInTier);
        onPerformanceFilterChange("all"); // Reset performance filter to "all" since we're using manual selection
    };

    const filteredAgents = searchTerm
        ? agents.filter(agent => agent.toLowerCase().includes(searchTerm.toLowerCase()))
        : agents;

    const getDisplayText = () => {
        const totalAgents = data.length;
        const hasAgentFiltering = selectedAgents.length < totalAgents;
        const hasPerformanceFiltering = performanceFilter !== "all";

        if (hasPerformanceFiltering && hasAgentFiltering) {
            const tierName = performanceFilter === "excellent" ? "Tier 1" :
                performanceFilter === "good" ? "Tier 2" :
                    performanceFilter === "warning" ? "Tier 3" : "Above T3";
            return `${tierName} + ${selectedAgents.length} agents`;
        } else if (hasPerformanceFiltering) {
            const tierName = performanceFilter === "excellent" ? "Tier 1" :
                performanceFilter === "good" ? "Tier 2" :
                    performanceFilter === "warning" ? "Tier 3" : "Above T3";
            return `${tierName} agents`;
        } else if (hasAgentFiltering) {
            if (selectedAgents.length === 0) return "No agents selected";
            if (selectedAgents.length === 1) return selectedAgents[0];
            return `${selectedAgents.length} of ${totalAgents} agents`;
        }
        return "All agents";
    };

    const getFilterSummary = () => {
        const totalAgents = data.length;
        const hasAgentFiltering = selectedAgents.length < totalAgents;
        const hasPerformanceFiltering = performanceFilter !== "all";

        if (!hasAgentFiltering && !hasPerformanceFiltering) {
            return `${totalAgents} agents`;
        }

        const parts = [];
        if (hasPerformanceFiltering) {
            const tierName = performanceFilter === "excellent" ? "Tier 1" :
                performanceFilter === "good" ? "Tier 2" :
                    performanceFilter === "warning" ? "Tier 3" : "Above T3";
            parts.push(tierName);
        }
        if (hasAgentFiltering) {
            parts.push(`${selectedAgents.length}/${totalAgents} agents`);
        }

        return parts.join(" • ");
    };

    return (
        <div className="relative" ref={dropdownRef}>
            <button
                onClick={() => setIsOpen(!isOpen)}
                className="flex items-center justify-between gap-2 px-3 py-1.5 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent min-w-[200px] max-w-[280px]"
                type="button"
            >
                <span className="flex-1 text-left truncate text-gray-900 dark:text-gray-100">
                    {getDisplayText()}
                </span>
                <svg
                    className={`w-4 h-4 text-gray-400 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
            </button>

            {isOpen && (
                <div className="absolute z-50 mt-1 w-[640px] bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg">
                    {/* Header */}
                    <div className="p-3 border-b border-gray-200 dark:border-gray-600">
                        <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                Agent & Performance Filter
                            </span>
                            <button
                                onClick={() => setIsOpen(false)}
                                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-1"
                                type="button"
                            >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                            {getFilterSummary()}
                        </div>
                        <div className="flex gap-2">
                            <button
                                onClick={handleSelectAll}
                                className="flex-1 px-2 py-1 text-xs bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 rounded hover:bg-blue-100 dark:hover:bg-blue-900/40 transition-colors"
                                type="button"
                            >
                                Select All
                            </button>
                            <button
                                onClick={() => {
                                    handleSelectNone();
                                    onPerformanceFilterChange("all");
                                }}
                                className="flex-1 px-2 py-1 text-xs bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 rounded hover:bg-red-100 dark:hover:bg-red-900/40 transition-colors"
                                type="button"
                            >
                                Clear All
                            </button>
                        </div>
                    </div>

                    {/* Horizontal Layout: Tiers Left, Agents Right */}
                    <div className="flex h-80">
                        {/* Performance Tiers Section - Left Panel */}
                        <div className="w-72 border-r border-gray-200 dark:border-gray-600 flex-shrink-0 flex flex-col">
                            <div className="p-3 border-b border-gray-200 dark:border-gray-600">
                                <div className="text-xs font-medium text-gray-600 dark:text-gray-400">PERFORMANCE TIERS</div>
                            </div>
                            <div className="flex-1 overflow-y-auto p-3">
                                <div className="space-y-2">
                                    <button
                                        onClick={() => handlePerformanceTierSelect("all")}
                                        className="w-full flex items-center p-3 hover:bg-blue-50 dark:hover:bg-blue-900/20 cursor-pointer transition-colors rounded-lg border border-blue-200 dark:border-blue-700 text-left"
                                        type="button"
                                    >
                                        <div className="flex-1">
                                            <div className="flex items-center gap-2 mb-1">
                                                <span className="w-3 h-3 rounded-full bg-blue-500"></span>
                                                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">All Performance Levels</span>
                                            </div>
                                            <div className="text-xs text-gray-500 dark:text-gray-400">Select all agents</div>
                                        </div>
                                        <span className="text-xs text-gray-500 dark:text-gray-400 ml-2 bg-gray-100 dark:bg-gray-600 px-2 py-1 rounded-full">
                                            {data.length}
                                        </span>
                                    </button>
                                    <button
                                        onClick={() => handleAddTierAgents("excellent")}
                                        className="w-full flex items-center p-3 hover:bg-green-50 dark:hover:bg-green-900/20 cursor-pointer transition-colors rounded-lg border border-green-200 dark:border-green-700 text-left"
                                        type="button"
                                    >
                                        <div className="flex-1">
                                            <div className="flex items-center gap-2 mb-1">
                                                <span className="w-3 h-3 rounded-full bg-green-500"></span>
                                                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">Tier 1</span>
                                            </div>
                                            <div className="text-xs text-gray-500 dark:text-gray-400">≤{config.dsat.excellent}%</div>
                                        </div>
                                        <span className="text-xs text-gray-500 dark:text-gray-400 ml-2 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-2 py-1 rounded-full">
                                            {performanceStats.totalExcellent}
                                        </span>
                                    </button>
                                    <button
                                        onClick={() => handleAddTierAgents("good")}
                                        className="w-full flex items-center p-3 hover:bg-yellow-50 dark:hover:bg-yellow-900/20 cursor-pointer transition-colors rounded-lg border border-yellow-200 dark:border-yellow-700 text-left"
                                        type="button"
                                    >
                                        <div className="flex-1">
                                            <div className="flex items-center gap-2 mb-1">
                                                <span className="w-3 h-3 rounded-full bg-yellow-500"></span>
                                                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">Tier 2</span>
                                            </div>
                                            <div className="text-xs text-gray-500 dark:text-gray-400">{(config.dsat.excellent + 0.01).toFixed(1)}-{config.dsat.good}%</div>
                                        </div>
                                        <span className="text-xs text-gray-500 dark:text-gray-400 ml-2 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 px-2 py-1 rounded-full">
                                            {performanceStats.totalGood}
                                        </span>
                                    </button>
                                    <button
                                        onClick={() => handleAddTierAgents("warning")}
                                        className="w-full flex items-center p-3 hover:bg-orange-50 dark:hover:bg-orange-900/20 cursor-pointer transition-colors rounded-lg border border-orange-200 dark:border-orange-700 text-left"
                                        type="button"
                                    >
                                        <div className="flex-1">
                                            <div className="flex items-center gap-2 mb-1">
                                                <span className="w-3 h-3 rounded-full bg-orange-500"></span>
                                                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">Tier 3</span>
                                            </div>
                                            <div className="text-xs text-gray-500 dark:text-gray-400">{(config.dsat.good + 0.01).toFixed(1)}-{config.dsat.target}%</div>
                                        </div>
                                        <span className="text-xs text-gray-500 dark:text-gray-400 ml-2 bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300 px-2 py-1 rounded-full">
                                            {performanceStats.totalWarning}
                                        </span>
                                    </button>
                                    <button
                                        onClick={() => handleAddTierAgents("poor")}
                                        className="w-full flex items-center p-3 hover:bg-red-50 dark:hover:bg-red-900/20 cursor-pointer transition-colors rounded-lg border border-red-200 dark:border-red-700 text-left"
                                        type="button"
                                    >
                                        <div className="flex-1">
                                            <div className="flex items-center gap-2 mb-1">
                                                <span className="w-3 h-3 rounded-full bg-red-500"></span>
                                                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">Add Above T3</span>
                                            </div>
                                            <div className="text-xs text-gray-500 dark:text-gray-400">&gt;{config.dsat.target}%</div>
                                        </div>
                                        <span className="text-xs text-gray-500 dark:text-gray-400 ml-2 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 px-2 py-1 rounded-full">
                                            {performanceStats.totalPoor}
                                        </span>
                                    </button>
                                </div>
                            </div>
                        </div>

                        {/* Individual Agents Section - Right Panel */}
                        <div className="flex-1 flex flex-col">
                            <div className="p-3 border-b border-gray-200 dark:border-gray-600">
                                <div className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">INDIVIDUAL AGENTS</div>
                                <input
                                    type="text"
                                    placeholder="Search agents..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                />
                            </div>
                            <div className="flex-1 overflow-y-auto p-2">
                                <div className="grid grid-cols-1 gap-1">
                                    {filteredAgents.map(agent => (
                                        <label
                                            key={agent}
                                            className="flex items-center p-2 hover:bg-gray-50 dark:hover:bg-gray-600 cursor-pointer transition-colors rounded"
                                        >
                                            <input
                                                type="checkbox"
                                                checked={selectedAgents.includes(agent)}
                                                onChange={() => handleToggleAgent(agent)}
                                                className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
                                            />
                                            <span className="text-sm text-gray-900 dark:text-gray-100 truncate flex-1">
                                                {agent}
                                            </span>
                                            {selectedAgents.includes(agent) && (
                                                <svg className="w-4 h-4 text-blue-500 ml-2" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                                </svg>
                                            )}
                                        </label>
                                    ))}
                                    {filteredAgents.length === 0 && (
                                        <div className="p-4 text-center text-gray-500 dark:text-gray-400 text-sm">
                                            No agents found
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export const DailyDSATTable: React.FC<DailyDSATTableProps> = ({ data, dates, mtdData, dailySurveyData, teamPerformanceData: biDataTeamPerformance }) => {
    const { config } = useDepartment();
    const [searchTerm, setSearchTerm] = useState("");
    const [dateRange, setDateRange] = useState<{ start: string; end: string } | null>(null);
    const [showPoster, setShowPoster] = useState(false);
    const [sortBy, setSortBy] = useState<string>("");
    const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
    const [filterPerformance, setFilterPerformance] = useState<"all" | "excellent" | "good" | "warning" | "poor">("all");
    const [selectedAgents, setSelectedAgents] = useState<string[]>([]);
    const [agentsInitialized, setAgentsInitialized] = useState(false);
    const [viewMode, setViewMode] = useState<"recent" | "all" | "range" | "currentWeek" | "lastWeek" | "currentMonth">(
        dates.length > 20 ? "recent" : "all"
    );

    // Initialize selectedAgents with all agents when data changes
    useEffect(() => {
        if (data.length > 0 && !agentsInitialized) {
            setSelectedAgents(data.map(agent => agent.name));
            setAgentsInitialized(true);
        }
    }, [data, agentsInitialized]);

    // Helper functions for week calculations
    const getWeekDateRange = useCallback((weekOffset: number): { start: Date; end: Date } => {
        const now = new Date();
        const currentDay = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
        const mondayOffset = currentDay === 0 ? -6 : 1 - currentDay; // Offset to get to Monday

        const monday = new Date(now);
        monday.setDate(now.getDate() + mondayOffset + (weekOffset * 7));
        monday.setHours(0, 0, 0, 0);

        const sunday = new Date(monday);
        sunday.setDate(monday.getDate() + 6);
        sunday.setHours(23, 59, 59, 999);

        return { start: monday, end: sunday };
    }, []);

    const formatDateForComparison = useCallback((date: Date): string => {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        return `${year}-${month}-${day}`;
    }, []);

    const getWeekDates = useCallback((weekOffset: number): string[] => {
        const { start, end } = getWeekDateRange(weekOffset);
        const startStr = formatDateForComparison(start);
        const endStr = formatDateForComparison(end);

        return dates.filter(date => {
            return date >= startStr && date <= endStr;
        });
    }, [dates, getWeekDateRange, formatDateForComparison]);

    // Function to calculate period average based on displayed dates
    const calculatePeriodAverage = useCallback((agent: DailyDSAT, datesToUse: string[]): number | null => {
        // Use survey-weighted calculation if we have daily survey data for APM imports
        if (dailySurveyData && dailySurveyData.length > 0) {
            let totalNegativeSurveys = 0;
            let totalSurveys = 0;

            datesToUse.forEach(date => {
                const agentDSAT = agent.dates[date];
                if (agentDSAT !== null) {
                    // Find the survey record for this agent and date
                    const surveyRecord = dailySurveyData.find(record =>
                        record.agent === agent.name && record.date === date
                    );

                    if (surveyRecord && surveyRecord.surveys > 0) {
                        const negativeSurveys = surveyRecord.surveys * (agentDSAT / 100);
                        totalNegativeSurveys += negativeSurveys;
                        totalSurveys += surveyRecord.surveys;
                    }
                }
            });

            if (totalSurveys > 0) {
                return (totalNegativeSurveys / totalSurveys) * 100;
            }
        }

        // Fallback to simple average for legacy data or when no survey data available
        const scores = datesToUse.map(date => agent.dates[date]).filter(score => score !== null) as number[];
        if (scores.length === 0) return null;
        return scores.reduce((sum, score) => sum + score, 0) / scores.length;
    }, [dailySurveyData]);

    // Helper function to get the original MTD average from mtdData (used only when not filtering)
    const getOfficialMTDAverage = useCallback((agentName: string): number | null => {
        if (!mtdData) return null;
        const mtdEntry = mtdData.find(entry => entry.name === agentName);
        return mtdEntry ? mtdEntry.dsat : null;
    }, [mtdData]);

    // Smart average function that adapts to current view
    const getAgentAverage = useCallback((agent: DailyDSAT, datesToUse: string[]): number | null => {
        // For Daily BI Data files, don't show MTD averages for individual agents
        // Daily BI Data is detected by the presence of team performance data (only Daily BI Data has this)
        if (biDataTeamPerformance && Object.keys(biDataTeamPerformance).length > 0) {
            return null;
        }

        // Special case: when showing all dates and we have official MTD data, use it
        if (viewMode === "all" && datesToUse.length === dates.length && mtdData) {
            const mtdAverage = getOfficialMTDAverage(agent.name);
            if (mtdAverage !== null) {
                return mtdAverage;
            }
        }

        // Otherwise calculate based on displayed dates
        return calculatePeriodAverage(agent, datesToUse);
    }, [biDataTeamPerformance, viewMode, dates.length, mtdData, getOfficialMTDAverage, calculatePeriodAverage]);

    // Get current month dates
    const getCurrentMonthDates = useCallback((): string[] => {
        const now = new Date();
        const currentYear = now.getFullYear();
        const currentMonth = now.getMonth() + 1; // getMonth() is 0-indexed

        return dates.filter(date => {
            const [year, month] = date.split('-').map(Number);
            return year === currentYear && month === currentMonth;
        });
    }, [dates]);

    // Smart date filtering based on view mode
    const displayDates = useMemo(() => {
        if (!dates.length) return [];

        switch (viewMode) {
            case "recent":
                // Show last 7 days
                return dates.slice(-7);
            case "currentWeek":
                // Show current week (Monday to Sunday)
                return getWeekDates(0);
            case "lastWeek":
                // Show last week (Monday to Sunday)
                return getWeekDates(-1);
            case "currentMonth":
                // Show current month's data
                return getCurrentMonthDates();
            case "range":
                if (dateRange) {
                    const startIndex = dates.indexOf(dateRange.start);
                    const endIndex = dates.indexOf(dateRange.end);
                    if (startIndex !== -1 && endIndex !== -1) {
                        const start = Math.min(startIndex, endIndex);
                        const end = Math.max(startIndex, endIndex);
                        return dates.slice(start, end + 1);
                    }
                }
                return dates.slice(-7); // Fallback to recent
            case "all":
            default:
                return dates;
        }
    }, [dates, viewMode, dateRange, getWeekDates, getCurrentMonthDates]);

    // Note: filteredDates was removed as the poster now uses displayDates directly

    const filteredAndSortedData = useMemo(() => {
        // Filter by search term and selected agents
        let filtered = data.filter(agent =>
            agent.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
            selectedAgents.includes(agent.name)
        );

        // Manual sorting (reverted from automatic MTD sorting)
        if (sortBy) {
            filtered = filtered.sort((a, b) => {
                let aValue: number | null;
                let bValue: number | null;

                if (sortBy === "name") {
                    return sortOrder === "asc"
                        ? a.name.localeCompare(b.name)
                        : b.name.localeCompare(a.name);
                } else if (sortBy === "average") {
                    // Sort by MTD average if available, otherwise calculate from daily data
                    aValue = getAgentAverage(a, displayDates);
                    bValue = getAgentAverage(b, displayDates);
                } else {
                    aValue = a.dates[sortBy] ?? null;
                    bValue = b.dates[sortBy] ?? null;
                }

                if (aValue === null && bValue === null) return 0;
                if (aValue === null) return 1;
                if (bValue === null) return -1;

                return sortOrder === "asc" ? aValue - bValue : bValue - aValue;
            });
        }

        return filtered;
    }, [data, searchTerm, sortBy, sortOrder, displayDates, selectedAgents, getAgentAverage]);

    // Team Performance calculations with survey-weighted DSAT
    const teamPerformanceData = useMemo(() => {
        // Calculate team daily DSAT for each date
        const teamDailyDSAT: { [date: string]: number } = {};
        const hasRealSurveyData = dailySurveyData && dailySurveyData.length > 0;
        const hasMTDSurveyData = mtdData && mtdData.some(agent => agent.surveys && agent.surveys > 0);
        const hasBIDataTeamPerformance = biDataTeamPerformance && Object.keys(biDataTeamPerformance).length > 0;

        // Determine if any filters are active (agent selection or performance filtering)
        const hasAgentFiltering = selectedAgents.length !== data.length;
        const hasPerformanceFiltering = filterPerformance !== "all";
        const hasAnyFiltering = hasAgentFiltering || hasPerformanceFiltering;

        // Get agents to use based on filtering status
        const agentsToUse = hasAnyFiltering ? filteredAndSortedData : data;
        const agentNamesToUse = agentsToUse.map(agent => agent.name);

        displayDates.forEach(date => {
            if (hasBIDataTeamPerformance && biDataTeamPerformance && biDataTeamPerformance[date] !== undefined && !hasAnyFiltering) {
                // Priority 1: Use Daily BI Data Total row values when available AND no filtering is active
                const biValue = biDataTeamPerformance[date];
                if (biValue !== null) {
                    teamDailyDSAT[date] = biValue;
                }
            } else if (hasRealSurveyData) {
                // Priority 2: Calculate survey-weighted daily team DSAT using REAL daily survey counts
                // Filter to only include agents from filtered data (respects both agent and performance filters)
                let totalNegativeSurveys = 0;
                let totalSurveys = 0;

                // Get all survey records for this specific date, filtered by agents in filteredAndSortedData
                const dateRecords = dailySurveyData.filter(record =>
                    record.date === date && agentNamesToUse.includes(record.agent)
                );

                dateRecords.forEach(record => {
                    if (record.dsat !== null && record.surveys > 0) {
                        const negativeSurveys = record.surveys * (record.dsat / 100);
                        totalNegativeSurveys += negativeSurveys;
                        totalSurveys += record.surveys;
                    }
                });

                if (totalSurveys > 0) {
                    teamDailyDSAT[date] = (totalNegativeSurveys / totalSurveys) * 100;
                }
            } else if (hasMTDSurveyData) {
                // Priority 3: Fallback: Estimate from MTD data (old method)
                // Use filtered data that respects both agent and performance filtering
                let totalNegativeSurveys = 0;
                let totalSurveys = 0;

                agentsToUse.forEach(agent => {
                    const agentDSAT = agent.dates[date];
                    if (agentDSAT !== null) {
                        const mtdEntry = mtdData.find(mtd => mtd.name === agent.name);
                        if (mtdEntry && mtdEntry.surveys && mtdEntry.surveys > 0) {
                            const estimatedDailySurveys = mtdEntry.surveys / displayDates.length;
                            const dailyNegativeSurveys = estimatedDailySurveys * (agentDSAT / 100);

                            totalNegativeSurveys += dailyNegativeSurveys;
                            totalSurveys += estimatedDailySurveys;
                        }
                    }
                });

                if (totalSurveys > 0) {
                    teamDailyDSAT[date] = (totalNegativeSurveys / totalSurveys) * 100;
                }
            } else {
                // Priority 4: Fallback to simple average when no survey data is available
                // Use filtered data that respects both agent and performance filtering
                const dayScores = agentsToUse
                    .map(agent => agent.dates[date])
                    .filter(score => score !== null) as number[];

                if (dayScores.length > 0) {
                    teamDailyDSAT[date] = dayScores.reduce((sum, score) => sum + score, 0) / dayScores.length;
                }
            }
        });

        // Calculate team average based on the filtered period
        let teamAverage = 0;

        if (hasBIDataTeamPerformance && !hasAnyFiltering) {
            // Priority 1: Calculate average from Daily BI Data Total row values when no filtering is active
            const validBIValues = displayDates
                .map(date => biDataTeamPerformance![date])
                .filter(value => value !== null) as number[];

            teamAverage = validBIValues.length > 0
                ? validBIValues.reduce((sum, value) => sum + value, 0) / validBIValues.length
                : 0;
        }
        // Priority 2: Special case: when showing all dates and we have official MTD data with surveys, use it
        else if (viewMode === "all" && displayDates.length === dates.length && hasMTDSurveyData && mtdData) {
            // Use official MTD data with survey weighting, filtered by agents in filteredAndSortedData
            let totalWeightedScore = 0;
            let totalSurveys = 0;

            mtdData.forEach(agent => {
                if (agent.dsat !== null && agent.surveys && agent.surveys > 0 && agentNamesToUse.includes(agent.name)) {
                    totalWeightedScore += agent.dsat * agent.surveys;
                    totalSurveys += agent.surveys;
                }
            });

            teamAverage = totalSurveys > 0 ? totalWeightedScore / totalSurveys : 0;
        }
        // Priority 3: Calculate from survey data
        else if (hasRealSurveyData) {
            // Calculate directly from raw daily survey data for the filtered period, filtered by agents in filteredAndSortedData
            let totalNegativeSurveys = 0;
            let totalSurveys = 0;

            displayDates.forEach(date => {
                const dateRecords = dailySurveyData.filter(record =>
                    record.date === date && agentNamesToUse.includes(record.agent)
                );
                dateRecords.forEach(record => {
                    if (record.dsat !== null && record.surveys > 0) {
                        const negativeSurveys = record.surveys * (record.dsat / 100);
                        totalNegativeSurveys += negativeSurveys;
                        totalSurveys += record.surveys;
                    }
                });
            });

            teamAverage = totalSurveys > 0 ? (totalNegativeSurveys / totalSurveys) * 100 : 0;
        } else {
            // Priority 4: Fallback to simple average of daily values for the filtered period
            const validDailyAverages = Object.values(teamDailyDSAT);
            teamAverage = validDailyAverages.length > 0
                ? validDailyAverages.reduce((sum, avg) => sum + avg, 0) / validDailyAverages.length
                : 0;
        }

        return {
            dailyDSAT: teamDailyDSAT,
            average: teamAverage,
            isWeighted: hasRealSurveyData || hasMTDSurveyData,
            isBIData: hasBIDataTeamPerformance && !hasAnyFiltering
        };
    }, [displayDates, dailySurveyData, biDataTeamPerformance, viewMode, dates.length, filteredAndSortedData, selectedAgents, data, filterPerformance, mtdData]);

    const handleSort = (column: string) => {
        if (sortBy === column) {
            setSortOrder(sortOrder === "asc" ? "desc" : "asc");
        } else {
            setSortBy(column);
            setSortOrder("asc");
        }
    };

    // Calculate performance statistics 
    const performanceStats = useMemo(() => {
        // Get all agent averages for the displayed period
        const allAgentAverages = data.map(agent => getAgentAverage(agent, displayDates)).filter(avg => avg !== null) as number[];

        // Get filtered agent averages for the displayed period  
        const filteredAverages = filteredAndSortedData.map(agent => getAgentAverage(agent, displayDates)).filter(avg => avg !== null) as number[];

        // Use the team performance average directly from the calculated teamPerformanceData
        const avgScore = teamPerformanceData.average;

        // Calculate tier counts for all agents (for dropdown)
        const totalExcellent = allAgentAverages.filter(score => score <= config.dsat.excellent).length; // Tier 1
        const totalGood = allAgentAverages.filter(score => score > config.dsat.excellent && score <= config.dsat.good).length; // Tier 2
        const totalWarning = allAgentAverages.filter(score => score > config.dsat.good && score <= config.dsat.target).length; // Tier 3
        const totalPoor = allAgentAverages.filter(score => score > config.dsat.target).length; // Above Tier 3

        // Calculate tier counts for filtered agents (for performance cards)
        const excellent = filteredAverages.filter(score => score <= config.dsat.excellent).length; // Tier 1
        const good = filteredAverages.filter(score => score > config.dsat.excellent && score <= config.dsat.good).length; // Tier 2
        const warning = filteredAverages.filter(score => score > config.dsat.good && score <= config.dsat.target).length; // Tier 3
        const poor = filteredAverages.filter(score => score > config.dsat.target).length; // Above Tier 3

        return {
            excellent,
            good,
            warning,
            poor,
            avgScore,
            total: filteredAverages.length,
            // For dropdown counts - always show total counts regardless of filtering
            totalExcellent,
            totalGood,
            totalWarning,
            totalPoor
        };
    }, [data, filteredAndSortedData, displayDates, getAgentAverage, teamPerformanceData.average, config.dsat.excellent, config.dsat.good, config.dsat.target]);

    useEffect(() => {
        if (dateRange) {
            setViewMode("range");
        }
    }, [dateRange]);

    // Update function calls to use department-specific values
    const teamPerformanceColor = getPerformanceColor(teamPerformanceData.average, config.dsat.excellent, config.dsat.good, config.dsat.target);

    return (
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700">
            {/* Header */}
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <div className="flex flex-col gap-4">
                    <div>
                        <h3 className="text-2xl font-bold text-gray-800 dark:text-gray-200">
                            Daily DSAT Performance
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                            Tier System: Green ≤{config.dsat.excellent}% • Yellow {(config.dsat.excellent + 0.01).toFixed(1)}-{config.dsat.target}% • Red &gt;{config.dsat.target}% • {filteredAndSortedData.length} agents • {displayDates.length} days shown
                        </p>
                        {dates.length > 1 && (
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                📊 Showing {viewMode === "recent" ? "last 7 days" : viewMode === "range" ? (dateRange ? `selected range (${dateRange.start} to ${dateRange.end})` : "last 7 days (select range below)") : "all dates"} of {dates.length} total days
                            </p>
                        )}
                    </div>

                    {/* Performance Stats */}
                    <div className="grid grid-cols-5 gap-2 text-center">
                        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-2 border border-gray-200 dark:border-gray-700">
                            <div className="text-lg font-bold text-gray-700 dark:text-gray-200">{performanceStats.total}</div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">Total</div>
                        </div>
                        <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-2 border border-green-200 dark:border-green-700">
                            <div className="text-lg font-bold text-green-600 dark:text-green-400">{performanceStats.excellent}</div>
                            <div className="text-xs text-green-600 dark:text-green-400">Tier 1</div>
                            <div className="text-xs text-green-500 dark:text-green-400 opacity-75">≤{config.dsat.excellent}% • ≤9min</div>
                        </div>
                        <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-2 border border-yellow-200 dark:border-yellow-700">
                            <div className="text-lg font-bold text-yellow-600 dark:text-yellow-400">{performanceStats.good}</div>
                            <div className="text-xs text-yellow-600 dark:text-yellow-400">Tier 2</div>
                            <div className="text-xs text-yellow-500 dark:text-yellow-400 opacity-75">≤{config.dsat.good}% • ≤10min</div>
                        </div>
                        <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-2 border border-orange-200 dark:border-orange-700">
                            <div className="text-lg font-bold text-orange-600 dark:text-orange-400">{performanceStats.warning}</div>
                            <div className="text-xs text-orange-600 dark:text-orange-400">Tier 3</div>
                            <div className="text-xs text-orange-500 dark:text-orange-400 opacity-75">≤{config.dsat.target}% • ≤11min</div>
                        </div>
                        <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-2 border border-red-200 dark:border-red-700">
                            <div className="text-lg font-bold text-red-600 dark:text-red-400">{performanceStats.poor}</div>
                            <div className="text-xs text-red-600 dark:text-red-400">Above T3</div>
                            <div className="text-xs text-red-500 dark:text-red-400 opacity-75">&gt;{config.dsat.target}% or &gt;11min</div>
                        </div>
                    </div>

                    {/* Controls Row */}
                    <div className="flex items-center justify-between gap-3">
                        <div className="flex items-center gap-2">
                            {/* View Mode Selector */}
                            {dates.length > 1 && (
                                <>
                                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                        View:
                                    </label>
                                    <select
                                        value={viewMode === "range" ? "all" : viewMode}
                                        onChange={(e) => {
                                            const newViewMode = e.target.value as typeof viewMode;
                                            setViewMode(newViewMode);
                                            if (dateRange) setDateRange(null);
                                        }}
                                        className="px-3 py-1.5 border border-gray-300 rounded-md text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    >
                                        <option value="recent">Recent 7 Days</option>
                                        <option value="currentWeek">This Week</option>
                                        <option value="lastWeek">Last Week</option>
                                        <option value="all">All {dates.length} Days</option>
                                    </select>
                                    <DateRangePicker
                                        availableDates={dates}
                                        selectedRange={dateRange}
                                        onRangeChange={(range) => {
                                            setDateRange(range);
                                            if (range) {
                                                setViewMode("range");
                                            } else {
                                                setViewMode("all");
                                            }
                                        }}
                                        customButtonText=""
                                        customButtonClassName={clsx(
                                            "flex items-center gap-2 px-3 py-1.5 border border-gray-300 rounded-md text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                                            viewMode === "range"
                                                ? "bg-blue-500 text-white border-blue-500 shadow-sm"
                                                : "bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600"
                                        )}
                                    />
                                </>
                            )}
                            {data.length > 0 && (
                                <>
                                    <AgentMultiSelect
                                        agents={data.map(agent => agent.name)}
                                        selectedAgents={selectedAgents}
                                        onSelectionChange={setSelectedAgents}
                                        performanceFilter={filterPerformance}
                                        onPerformanceFilterChange={setFilterPerformance}
                                        performanceStats={performanceStats}
                                        config={config}
                                        data={data}
                                        displayDates={displayDates}
                                        getAgentAverage={getAgentAverage}
                                    />
                                </>
                            )}
                        </div>

                        {data.length > 0 && (
                            <div className="flex items-center gap-2">
                                <input
                                    type="text"
                                    placeholder="Search agents..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="px-3 py-1.5 border border-gray-300 rounded-md text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent w-56"
                                />
                                <button
                                    onClick={() => setShowPoster(!showPoster)}
                                    className="inline-flex items-center gap-1.5 px-3 py-1.5 bg-purple-600 hover:bg-purple-700 text-white rounded-md font-medium transition-colors duration-200 text-sm"
                                >
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                    Poster
                                </button>
                            </div>
                        )}
                    </div>
                </div>
            </div>

            {/* Performance Status */}
            <PerformanceStatus
                totalDates={dates.length}
                displayedDates={displayDates.length}
                totalAgents={filteredAndSortedData.length}
                viewMode={viewMode}
                onRecommendedModeChange={(mode) => {
                    setViewMode(mode as "recent" | "currentMonth");
                    if (dateRange) setDateRange(null);
                }}
            />

            {/* Table */}
            <div className="overflow-x-auto" style={{
                scrollbarWidth: 'thin',
                scrollbarColor: '#CBD5E0 #F7FAFC'
            }}>
                <table className="w-full">
                    <thead className="bg-gray-50 dark:bg-gray-900 sticky top-0">
                        <tr>
                            <th className="px-6 py-4 text-left text-xs font-bold text-gray-600 dark:text-gray-300 uppercase tracking-wider sticky left-0 bg-white dark:bg-gray-800 z-40 border-r border-gray-200 dark:border-gray-700 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800" onClick={() => handleSort("name")}>
                                <div className="flex items-center">
                                    <span>Agent</span>
                                    {sortBy === "name" && (
                                        <span className="ml-2 text-blue-500">
                                            {sortOrder === "asc" ? "↑" : "↓"}
                                        </span>
                                    )}
                                </div>
                            </th>
                            {displayDates.length > 0 && (
                                <th className="px-3 py-4 text-center text-xs font-bold text-gray-600 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800" onClick={() => handleSort("average")}>
                                    <div className="flex items-center justify-center gap-2">
                                        <span>
                                            {viewMode === "all" && displayDates.length === dates.length
                                                ? "MTD Avg"
                                                : viewMode === "recent"
                                                    ? "7-Day Avg"
                                                    : viewMode === "currentWeek"
                                                        ? "This Week Avg"
                                                        : viewMode === "lastWeek"
                                                            ? "Last Week Avg"
                                                            : viewMode === "currentMonth"
                                                                ? "Month Avg"
                                                                : "Range Avg"}
                                        </span>
                                        {sortBy === "average" && (
                                            <span className="text-blue-500">
                                                {sortOrder === "asc" ? "↑" : "↓"}
                                            </span>
                                        )}
                                    </div>
                                </th>
                            )}
                            {displayDates.map(date => (
                                <th
                                    key={date}
                                    className="px-3 py-4 text-center text-xs font-bold text-gray-600 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 min-w-[80px]"
                                    onClick={() => handleSort(date)}
                                >
                                    <div className="flex items-center justify-center gap-2">
                                        <span className="whitespace-nowrap">{date}</span>
                                        {sortBy === date && (
                                            <span className="text-blue-500">
                                                {sortOrder === "asc" ? "↑" : "↓"}
                                            </span>
                                        )}
                                    </div>
                                </th>
                            ))}
                        </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                        {/* Team Performance Section */}
                        <tr className="bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 border-l-4 border-purple-500">
                            <td className="px-6 py-4 whitespace-nowrap sticky left-0 bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 z-30 border-r border-gray-200 dark:border-gray-700 min-w-[200px]">
                                <div className="flex items-center">
                                    <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center text-white font-bold text-sm mr-3 flex-shrink-0">
                                        TP
                                    </div>
                                    <div className="font-bold text-purple-800 dark:text-purple-300 flex-1 min-w-0">
                                        <div className="truncate">
                                            👑 Team Performance
                                        </div>
                                    </div>
                                </div>
                            </td>
                            {displayDates.length > 0 && (
                                <td className="px-3 py-4 whitespace-nowrap text-center bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 relative z-20">
                                    {teamPerformanceData.isBIData ? (
                                        <div className="text-gray-400 text-sm font-medium">
                                            -
                                        </div>
                                    ) : teamPerformanceData.isWeighted ? (
                                        <div>
                                            <div className={clsx(
                                                "text-sm font-bold",
                                                teamPerformanceColor
                                            )}>
                                                {teamPerformanceData.average.toFixed(2)}%
                                            </div>
                                            {dailySurveyData && dailySurveyData.length > 0 && (() => {
                                                // Use filteredAndSortedData agents to respect both agent and performance filtering
                                                const filteredAgentNames = filteredAndSortedData.map(agent => agent.name);
                                                const totalSurveys = dailySurveyData.filter(record =>
                                                    displayDates.includes(record.date) && filteredAgentNames.includes(record.agent)
                                                ).reduce((sum, record) => sum + record.surveys, 0);
                                                const positiveSurveys = dailySurveyData.filter(record =>
                                                    displayDates.includes(record.date) && filteredAgentNames.includes(record.agent)
                                                ).reduce((sum, record) => {
                                                    if (record.dsat !== null && record.surveys > 0) {
                                                        return sum + Math.round(record.surveys * (1 - record.dsat / 100));
                                                    }
                                                    return sum;
                                                }, 0);
                                                const negativeSurveys = totalSurveys - positiveSurveys;
                                                return totalSurveys > 0 ? (
                                                    <div className="flex items-center justify-center gap-1.5 mt-1">
                                                        <div className="min-w-[28px] h-6 bg-emerald-100 dark:bg-emerald-900/30 border border-emerald-300 dark:border-emerald-700 rounded-full flex items-center justify-center text-emerald-700 dark:text-emerald-300 text-xs font-semibold px-1">
                                                            {positiveSurveys}
                                                        </div>
                                                        <div className="min-w-[28px] h-6 bg-red-100 dark:bg-red-900/30 border border-red-300 dark:border-red-700 rounded-full flex items-center justify-center text-red-700 dark:text-red-300 text-xs font-semibold px-1">
                                                            {negativeSurveys}
                                                        </div>
                                                    </div>
                                                ) : null;
                                            })()}
                                        </div>
                                    ) : (
                                        <div className="text-gray-400 text-sm font-medium">
                                            -
                                        </div>
                                    )}
                                </td>
                            )}
                            {displayDates.map(date => {
                                const teamScore = teamPerformanceData.dailyDSAT[date];
                                return (
                                    <td
                                        key={date}
                                        className="px-3 py-4 whitespace-nowrap text-center bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 relative z-20"
                                    >
                                        {teamScore !== undefined ? (
                                            <div>
                                                <div className={clsx(
                                                    "text-sm font-bold",
                                                    getPerformanceColor(teamScore, config.dsat.excellent, config.dsat.good, config.dsat.target)
                                                )}>
                                                    {teamScore.toFixed(2)}%
                                                </div>
                                                {dailySurveyData && dailySurveyData.length > 0 && (() => {
                                                    // Use filteredAndSortedData agents to respect both agent and performance filtering
                                                    const filteredAgentNames = filteredAndSortedData.map(agent => agent.name);
                                                    const totalSurveys = dailySurveyData.filter(record =>
                                                        record.date === date && filteredAgentNames.includes(record.agent)
                                                    ).reduce((sum, record) => sum + record.surveys, 0);
                                                    const positiveSurveys = dailySurveyData.filter(record =>
                                                        record.date === date && filteredAgentNames.includes(record.agent)
                                                    ).reduce((sum, record) => {
                                                        if (record.dsat !== null && record.surveys > 0) {
                                                            return sum + Math.round(record.surveys * (1 - record.dsat / 100));
                                                        }
                                                        return sum;
                                                    }, 0);
                                                    const negativeSurveys = totalSurveys - positiveSurveys;
                                                    return totalSurveys > 0 ? (
                                                        <div className="flex items-center justify-center gap-1.5 mt-1">
                                                            <div className="min-w-[28px] h-6 bg-emerald-100 dark:bg-emerald-900/30 border border-emerald-300 dark:border-emerald-700 rounded-full flex items-center justify-center text-emerald-700 dark:text-emerald-300 text-xs font-semibold px-1">
                                                                {positiveSurveys}
                                                            </div>
                                                            <div className="min-w-[28px] h-6 bg-red-100 dark:bg-red-900/30 border border-red-300 dark:border-red-700 rounded-full flex items-center justify-center text-red-700 dark:text-red-300 text-xs font-semibold px-1">
                                                                {negativeSurveys}
                                                            </div>
                                                        </div>
                                                    ) : null;
                                                })()}
                                            </div>
                                        ) : (
                                            <div className="text-gray-400 text-sm font-medium">
                                                -
                                            </div>
                                        )}
                                    </td>
                                );
                            })}
                        </tr>

                        {filteredAndSortedData.map((agent) => {
                            return (
                                <tr
                                    key={agent.name}
                                    className="hover:bg-gray-50 dark:hover:bg-gray-900/50 transition-colors"
                                >
                                    <td className="px-6 py-4 whitespace-nowrap sticky left-0 bg-white dark:bg-gray-800 z-20 border-r border-gray-200 dark:border-gray-700 min-w-[200px]">
                                        <div className="flex items-center">
                                            <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center text-blue-600 dark:text-blue-300 font-bold text-sm mr-3 flex-shrink-0">
                                                {agent.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                                            </div>
                                            <div className="font-medium text-gray-900 dark:text-gray-100 flex-1 min-w-0">
                                                <div className="truncate" title={agent.name}>
                                                    {agent.name}
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    {displayDates.length > 0 && (
                                        <td
                                            className={clsx(
                                                "px-3 py-4 whitespace-nowrap text-center",
                                                getPerformanceBg(getAgentAverage(agent, displayDates), config.dsat.excellent, config.dsat.good, config.dsat.target)
                                            )}
                                        >
                                            {(() => {
                                                const mtdAvg = getAgentAverage(agent, displayDates);
                                                return mtdAvg !== null ? (
                                                    <div>
                                                        <div className={clsx(
                                                            "text-sm font-bold",
                                                            getPerformanceColor(mtdAvg, config.dsat.excellent, config.dsat.good, config.dsat.target)
                                                        )}>
                                                            {mtdAvg.toFixed(2)}%
                                                        </div>
                                                        {dailySurveyData && dailySurveyData.length > 0 && (() => {
                                                            const totalSurveys = dailySurveyData.filter(record =>
                                                                displayDates.includes(record.date) && record.agent === agent.name
                                                            ).reduce((sum, record) => sum + record.surveys, 0);
                                                            const positiveSurveys = dailySurveyData.filter(record =>
                                                                displayDates.includes(record.date) && record.agent === agent.name
                                                            ).reduce((sum, record) => {
                                                                if (record.dsat !== null && record.surveys > 0) {
                                                                    return sum + Math.round(record.surveys * (1 - record.dsat / 100));
                                                                }
                                                                return sum;
                                                            }, 0);
                                                            const negativeSurveys = totalSurveys - positiveSurveys;
                                                            return totalSurveys > 0 ? (
                                                                <div className="flex items-center justify-center gap-1.5 mt-1">
                                                                    <div className="min-w-[28px] h-6 bg-emerald-100 dark:bg-emerald-900/30 border border-emerald-300 dark:border-emerald-700 rounded-full flex items-center justify-center text-emerald-700 dark:text-emerald-300 text-xs font-semibold px-1">
                                                                        {positiveSurveys}
                                                                    </div>
                                                                    <div className="min-w-[28px] h-6 bg-red-100 dark:bg-red-900/30 border border-red-300 dark:border-red-700 rounded-full flex items-center justify-center text-red-700 dark:text-red-300 text-xs font-semibold px-1">
                                                                        {negativeSurveys}
                                                                    </div>
                                                                </div>
                                                            ) : null;
                                                        })()}
                                                    </div>
                                                ) : (
                                                    <div className="text-gray-400 text-sm font-medium">
                                                        -
                                                    </div>
                                                );
                                            })()}
                                        </td>
                                    )}
                                    {displayDates.map(date => {
                                        const score = agent.dates[date];
                                        const surveyRecord = dailySurveyData?.find(record =>
                                            record.date === date && record.agent === agent.name
                                        );
                                        return (
                                            <td
                                                key={date}
                                                className={clsx(
                                                    "px-3 py-4 whitespace-nowrap text-center",
                                                    getPerformanceBg(score, config.dsat.excellent, config.dsat.good, config.dsat.target)
                                                )}
                                            >
                                                {score !== null ? (
                                                    <div>
                                                        <div className={clsx(
                                                            "text-sm font-bold",
                                                            getPerformanceColor(score, config.dsat.excellent, config.dsat.good, config.dsat.target)
                                                        )}>
                                                            {score.toFixed(2)}%
                                                        </div>
                                                        {dailySurveyData && dailySurveyData.length > 0 && surveyRecord && surveyRecord.surveys > 0 && (
                                                            <div className="flex items-center justify-center gap-1.5 mt-1">
                                                                <div className="min-w-[28px] h-6 bg-emerald-100 dark:bg-emerald-900/30 border border-emerald-300 dark:border-emerald-700 rounded-full flex items-center justify-center text-emerald-700 dark:text-emerald-300 text-xs font-semibold px-1">
                                                                    {surveyRecord.dsat !== null && surveyRecord.surveys > 0
                                                                        ? Math.round(surveyRecord.surveys * (1 - surveyRecord.dsat / 100))
                                                                        : 0}
                                                                </div>
                                                                <div className="min-w-[28px] h-6 bg-red-100 dark:bg-red-900/30 border border-red-300 dark:border-red-700 rounded-full flex items-center justify-center text-red-700 dark:text-red-300 text-xs font-semibold px-1">
                                                                    {surveyRecord.dsat !== null && surveyRecord.surveys > 0
                                                                        ? Math.round(surveyRecord.surveys * (surveyRecord.dsat / 100))
                                                                        : 0}
                                                                </div>
                                                            </div>
                                                        )}
                                                    </div>
                                                ) : (
                                                    <div className="text-gray-400 text-sm font-medium">
                                                        -
                                                    </div>
                                                )}
                                            </td>
                                        );
                                    })}
                                </tr>
                            );
                        })}
                    </tbody>
                </table>
            </div>

            {filteredAndSortedData.length === 0 && (
                <div className="p-8 text-center text-gray-500 dark:text-gray-400">
                    No agents found matching your search criteria.
                </div>
            )}

            {/* Date Range Info */}
            {displayDates.length > 0 && dates.length > displayDates.length && (
                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border-t border-gray-200 dark:border-gray-700">
                    <div className="text-sm text-blue-700 dark:text-blue-300 text-center">
                        📅 Showing {displayDates.length} of {dates.length} total days
                        {displayDates.length > 0 && ` (${displayDates[0]} to ${displayDates[displayDates.length - 1]})`}
                        {viewMode === "all" && dates.length > 35 && (
                            <span className="block mt-1 text-xs">Tip: Use &ldquo;Recent 7&rdquo;, &ldquo;This Month&rdquo;, or &ldquo;Range&rdquo; view for better performance with many dates</span>
                        )}
                    </div>
                </div>
            )}

            {/* Help text about calculations */}
            <div className="p-3 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 text-xs text-gray-500 dark:text-gray-400 text-center">
                <p>
                    {viewMode === "all" && displayDates.length === dates.length
                        ? "Showing official MTD averages from uploaded data"
                        : `Showing calculated averages for the ${displayDates.length} selected date(s)`}
                </p>
            </div>

            {/* Poster Modal */}
            {showPoster && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                    <div className="bg-white dark:bg-gray-900 rounded-2xl shadow-2xl max-w-full max-h-full overflow-auto">
                        <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                            <h2 className="text-xl font-bold text-gray-800 dark:text-gray-200">
                                Daily DSAT Poster
                            </h2>
                            <button
                                onClick={() => setShowPoster(false)}
                                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                            >
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                        <div className="p-4">
                            <DailyDSATPoster
                                data={data}
                                dates={dates}
                                filteredData={filteredAndSortedData}
                                searchTerm={searchTerm || undefined}
                                mtdData={mtdData}
                                dailySurveyData={dailySurveyData}
                                teamPerformanceData={teamPerformanceData}
                                performanceStats={{
                                    excellent: performanceStats.excellent,
                                    good: performanceStats.good,
                                    warning: performanceStats.warning,
                                    needsImprovement: performanceStats.poor,
                                    avgScore: performanceStats.avgScore,
                                    total: performanceStats.total
                                }}
                                displayDates={displayDates}
                                sortedAndFilteredData={filteredAndSortedData}
                                getAgentAverage={getAgentAverage}
                            />
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}; 